# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Brat Cover Generator** - a frontend-only web application that creates custom "brat-style" album covers (inspired by <PERSON><PERSON><PERSON>'s aesthetic). Users can input text, customize colors and fonts, and download high-quality PNG covers entirely in the browser.

## Architecture

**Frontend-Only Application (Next.js 15 + TypeScript)**
- Located in `/frontend/` directory
- App Router with TypeScript and Tailwind CSS
- Client-side SVG generation and PNG conversion
- Real-time preview without network requests
- Instant download functionality
- No backend dependencies for core functionality

## Development Commands

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server with Turbopack
npm run build        # Production build
npm run lint         # ESLint check
npm test             # Jest tests
```

### Backend (Optional - Legacy)
The backend directory exists for template purposes but is not required for the brat generator functionality. All image generation happens in the frontend.

## Key Technical Details

### Image Generation Flow
1. User inputs → Frontend collects parameters (text, backgroundColor, textColor, fontSize, fontFamily)
2. `BratGenerator.generateSVG()` creates SVG with proper text wrapping and font rendering
3. `BratGenerator.generatePNG()` converts SVG to PNG for real-time preview
4. `BratGenerator.downloadPNG()` triggers instant client-side download
5. All operations happen in browser - no network requests needed

### Core Services
- `/src/services/bratGenerator.ts` - Complete SVG generation and PNG conversion
- Text wrapping algorithm for optimal layout
- Canvas-based PNG conversion with high quality settings
- Instant download without backend dependency

### Environment Configuration
**Frontend:** No API URLs needed - completely self-contained
**Deployment:** Can be deployed as static site (Vercel, Netlify, etc.)

## File Structure Patterns

**Frontend Components:**
- `/src/app/page.tsx` - Main generator interface with real-time preview
- `/src/app/blog/page.tsx` - Blog listing page with brat-themed content  
- `/src/app/blog/[slug]/page.tsx` - Dynamic blog detail pages with SEO optimization
- `/src/services/bratGenerator.ts` - Complete image generation service (SVG + PNG)
- `/src/services/blogService.ts` - Blog data service with 3000+ word articles
- `/src/components/` - Reusable components (Navbar, ScrollReveal animations)

**Legacy Backend (Optional):**
- Backend directory exists for template purposes but core functionality doesn't use it
- Can be completely removed for pure frontend deployment

## Styling Guidelines

- **Color Scheme:** Green/lime gradient theme (`from-green-500 to-lime-500`)
- **Typography:** Clean, modern fonts with proper hierarchy
- **Layout:** Responsive grid with customization panel and large preview area
- **Animations:** ScrollReveal components for smooth page interactions

## Special Considerations

### Image Quality
- SVG generation ensures crisp text at any size
- PNG conversion maintains high quality (800x800px output)
- Font fallbacks for cross-platform compatibility

### Performance
- 500ms debounce on preview generation for smooth UX
- Instant client-side image generation (no network latency)
- Canvas-based PNG conversion with high quality settings
- Static site deployment for global CDN distribution

### Security
- Input validation on text length (100 char limit)
- XSS protection through proper XML escaping in SVG generation
- No sensitive data processing - all operations client-side
- No API surface area to attack

## Development Workflow

1. **Start frontend only:** `npm run dev` for development server on :3000
2. **No backend needed:** All functionality works entirely in the browser
3. **Real-time preview:** Changes trigger immediate local preview generation
4. **Testing:** Focus on UI components and image generation logic
5. **Deployment:** Deploy as static site to Vercel, Netlify, or any CDN

## Common Tasks

**Adding new color presets:** Update `defaultColors` arrays in both `/src/services/bratGenerator.ts` and `/src/app/page.tsx`
**Modifying text layout:** Edit SVG generation logic in `/src/services/bratGenerator.ts`
**Changing fonts:** Update `fontOptions` array in `/src/app/page.tsx` (uses web-safe fonts)
**Image generation updates:** Modify `BratGenerator` class methods for SVG/PNG generation
**Adding new blog posts:** Add articles to `blogArticles` array in `/src/services/blogService.ts`
**Blog content updates:** Edit individual article content, meta data, and images in blogService.ts

## Blog System

The blog system is **completely frontend-based** with no API dependencies:

### Content Management
- **6 detailed articles** (3000+ words each) stored in `/src/services/blogService.ts`
- **Local data service** with functions for article retrieval, filtering, and navigation
- **No database or CMS required** - all content is version-controlled

### Technical Features
- **Dynamic routing** via `/blog/[slug]` for SEO-friendly URLs
- **Static generation** for all blog posts during build time via `generateStaticParams`
- **SEO optimization** with meta tags, Open Graph, Twitter Cards, and JSON-LD structured data
- **Featured images and galleries** using Unsplash for visual content
- **Social sharing** buttons for Twitter, Facebook, LinkedIn
- **Article navigation** with previous/next links and related articles
- **Responsive design** optimized for reading on all devices

### Data Flow
1. **Build time**: `blogService.ts` provides article data to `generateStaticParams`
2. **Static pages**: All blog pages pre-rendered as static HTML
3. **Runtime**: Client-side filtering and navigation using local data
4. **No API calls**: Everything works offline after initial page load

## Design System

The application uses a cohesive brat-inspired design with:
- Bright, bold color combinations (especially lime/green)
- Clean typography with generous spacing
- Smooth animations and micro-interactions
- Mobile-first responsive design
- Consistent component styling across pages