# Deployment Guide

This guide covers deploying the Template App to various cloud platforms.

## 🌐 Overview

The Template App consists of two parts:
- **Frontend**: Next.js application (React)
- **Backend**: Node.js/Express API

Both can be deployed separately to different platforms or together on full-stack hosting.

## 📱 Frontend Deployment

### Vercel (Recommended)

Vercel is the creators of Next.js and provides the best Next.js hosting experience.

#### Deploy via GitHub
1. Push your code to GitHub
2. Visit [vercel.com](https://vercel.com)
3. Click "New Project" and import your repository
4. Configure build settings:
   ```
   Framework: Next.js
   Root Directory: frontend
   Build Command: npm run build
   Output Directory: .next
   ```
5. Set environment variables:
   ```
   NEXT_PUBLIC_API_URL=https://your-backend-url.com
   ```
6. Deploy!

#### Deploy via CLI
```bash
# Install Vercel CLI
npm install -g vercel

# Navigate to frontend directory
cd frontend

# Deploy
vercel

# Set production environment variables
vercel env add NEXT_PUBLIC_API_URL production
```

### Netlify

1. Push code to GitHub/GitLab
2. Connect repository to Netlify
3. Configure build settings:
   ```
   Base directory: frontend
   Build command: npm run build
   Publish directory: frontend/.next
   ```
4. Set environment variables in Netlify dashboard

### AWS Amplify

1. Connect your GitHub repository
2. Configure build settings:
   ```
   baseDirectory: frontend
   commands:
     - npm ci
     - npm run build
   artifacts:
     baseDirectory: .next
     files:
       - '**/*'
   ```

## 🔧 Backend Deployment

### Railway (Recommended)

Railway provides simple deployment for Node.js applications.

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login
railway login

# Navigate to backend directory
cd backend

# Initialize project
railway init

# Deploy
railway up

# Set environment variables
railway variables set JWT_SECRET=your-secret-key
railway variables set FRONTEND_URL=https://your-frontend-url.com
```

### Heroku

```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set JWT_SECRET=your-secret-key
heroku config:set FRONTEND_URL=https://your-frontend-url.com

# Create Procfile in backend directory
echo "web: npm start" > Procfile

# Deploy
git subtree push --prefix backend heroku main
```

### DigitalOcean App Platform

1. Create new app on DigitalOcean
2. Connect your repository
3. Configure component:
   ```
   Type: Service
   Source Directory: backend
   Build Command: npm run build
   Run Command: npm start
   ```
4. Set environment variables in dashboard

### AWS Elastic Beanstalk

1. Install EB CLI: `pip install awsebcli`
2. Navigate to backend directory
3. Initialize: `eb init`
4. Create environment: `eb create production`
5. Deploy: `eb deploy`

## 🐳 Docker Deployment

### Frontend Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["npm", "start"]
```

### Backend Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "start"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - JWT_SECRET=your-secret-key
      - FRONTEND_URL=http://localhost:3000
```

## ☁️ Full-Stack Platforms

### Render

Deploy both frontend and backend on Render:

1. **Backend Service**:
   ```
   Build Command: npm install && npm run build
   Start Command: npm start
   Root Directory: backend
   ```

2. **Frontend Static Site**:
   ```
   Build Command: npm install && npm run build
   Publish Directory: frontend/.next
   Root Directory: frontend
   ```

### Fly.io

1. Install flyctl CLI
2. Create fly.toml for each service
3. Deploy with `fly deploy`

## 🗄️ Database Deployment

### PostgreSQL

#### Supabase (Recommended)
1. Create project at [supabase.com](https://supabase.com)
2. Get connection string from dashboard
3. Add to environment variables:
   ```
   DATABASE_URL=postgresql://postgres:[password]@[host]:5432/postgres
   ```

#### Railway PostgreSQL
```bash
railway add postgresql
railway variables set DATABASE_URL=$RAILWAY_DATABASE_URL
```

#### Heroku Postgres
```bash
heroku addons:create heroku-postgresql:hobby-dev
```

### MongoDB

#### MongoDB Atlas
1. Create cluster at [mongodb.com](https://cloud.mongodb.com)
2. Get connection string
3. Add to environment variables:
   ```
   MONGODB_URI=mongodb+srv://username:<EMAIL>/dbname
   ```

## 📧 Email Service Setup

### SendGrid
```bash
# Environment variables
EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=your-api-key
FROM_EMAIL=<EMAIL>
```

### Mailgun
```bash
# Environment variables
EMAIL_SERVICE=mailgun
MAILGUN_API_KEY=your-api-key
MAILGUN_DOMAIN=yourdomain.com
```

## 🔒 SSL/HTTPS

Most platforms provide automatic SSL:

- **Vercel**: Automatic SSL for all domains
- **Netlify**: Automatic SSL with Let's Encrypt
- **Railway**: Automatic SSL for generated domains
- **Heroku**: Automatic SSL for Heroku domains

## 📊 Monitoring & Analytics

### Frontend Analytics
```javascript
// Google Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

// Vercel Analytics
npm install @vercel/analytics
```

### Backend Monitoring
```javascript
// Sentry for error tracking
npm install @sentry/node

// LogRocket for session replay
npm install logrocket
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: cd frontend && npm ci
      - run: cd frontend && npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: cd backend && npm ci
      - run: cd backend && npm run build
      - name: Deploy to Railway
        run: railway up --service backend
```

## 🌍 Domain Configuration

### Custom Domain Setup

1. **Frontend (Vercel)**:
   - Add domain in Vercel dashboard
   - Update DNS records as instructed

2. **Backend (Railway)**:
   - Add custom domain in Railway dashboard
   - Configure DNS CNAME record

3. **Full Setup**:
   ```
   yourdomain.com → Frontend (Vercel)
   api.yourdomain.com → Backend (Railway)
   ```

### Environment Variables Update
```env
# Frontend
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Backend
FRONTEND_URL=https://yourdomain.com
```

## 🚀 Performance Optimization

### Frontend
- Enable Vercel Analytics
- Use Next.js Image optimization
- Implement proper caching headers
- Use CDN for static assets

### Backend
- Enable compression middleware
- Implement Redis caching
- Use database connection pooling
- Add response caching

## 🔧 Troubleshooting

### Common Issues

1. **CORS Errors**:
   ```javascript
   // Update CORS configuration
   app.use(cors({
     origin: ['https://yourdomain.com', 'https://www.yourdomain.com'],
     credentials: true
   }));
   ```

2. **Environment Variables**:
   - Ensure all required variables are set
   - Check variable names match exactly
   - Restart services after changing variables

3. **Build Failures**:
   - Check Node.js version compatibility
   - Verify package.json scripts
   - Review build logs for specific errors

### Health Checks
```bash
# Test frontend
curl https://yourdomain.com

# Test backend
curl https://api.yourdomain.com/health

# Test API endpoints
curl -H "Authorization: Bearer <token>" https://api.yourdomain.com/api/status
```

## 📈 Scaling

### Frontend Scaling
- Vercel automatically scales
- Use Vercel Edge Functions for dynamic content
- Implement ISR (Incremental Static Regeneration)

### Backend Scaling
- Horizontal scaling with load balancers
- Database read replicas
- Redis for session storage and caching
- Microservices architecture

---

This deployment guide covers the most common scenarios. Choose the platforms that best fit your needs and budget. For production applications, always implement proper monitoring, backup strategies, and security measures.