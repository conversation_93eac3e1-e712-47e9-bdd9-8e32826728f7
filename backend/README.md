# Template App Backend

RESTful API backend built with Node.js, Express, and TypeScript.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 📁 Project Structure

```
src/
├── controllers/            # Request handlers
│   ├── AuthController.ts   # Authentication logic
│   └── ApiController.ts    # General API logic
├── routes/                # API routes
│   ├── auth.ts            # Auth endpoints
│   └── api.ts             # General endpoints
├── services/              # Business logic
│   └── AuthService.ts     # Authentication service
├── middleware/            # Express middleware
│   ├── auth.ts            # JWT authentication
│   ├── validation.ts      # Input validation
│   └── errorHandler.ts    # Error handling
├── utils/                 # Utility functions
│   └── ApiResponse.ts     # Standardized responses
└── index.ts               # Server entry point
```

## 🔐 Authentication

JWT-based authentication with email verification:

```typescript
// Send verification code
POST /api/auth/send-code
{
  "email": "<EMAIL>"
}

// Verify code and get token
POST /api/auth/verify
{
  "email": "<EMAIL>",
  "code": "123456"
}

// Use token in requests
Authorization: Bearer <jwt-token>
```

## 📚 API Endpoints

### Authentication
- `POST /api/auth/send-code` - Send verification code
- `POST /api/auth/verify` - Verify code and login
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/me` - Get current user (protected)
- `POST /api/auth/logout` - Logout user (protected)

### General
- `GET /health` - Server health check
- `GET /api/status` - API status
- `GET /api/posts` - Get paginated posts
- `GET /api/posts/:id` - Get specific post
- `GET /api/user/profile` - Get user profile (protected)
- `PUT /api/user/profile` - Update user profile (protected)

## 🛠️ Available Scripts

- `npm run dev` - Start development server with nodemon
- `npm run build` - Build TypeScript to JavaScript
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

## 🔧 Configuration

### Environment Variables
Required variables in `.env`:

```env
# Server
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# Optional: Database
DATABASE_URL=postgresql://...

# Optional: Email
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-password
```

### Security Features
- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Request rate limiting
- **Input Validation**: Request validation
- **JWT Authentication**: Secure token-based auth

## 🏗️ Architecture

### Controllers
Handle HTTP requests and responses:

```typescript
export class AuthController {
  public sendVerificationCode = async (req: Request, res: Response) => {
    // Handle request logic
  };
}
```

### Services
Business logic and data processing:

```typescript
export class AuthService {
  public async sendVerificationCode(email: string) {
    // Business logic
  }
}
```

### Middleware
Request processing pipeline:

```typescript
export const authMiddleware = (req, res, next) => {
  // Verify JWT token
  // Set req.user
  next();
};
```

## 📊 Error Handling

Standardized error responses:

```typescript
// Success response
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2025-01-15T..."
}

// Error response
{
  "success": false,
  "error": {
    "message": "Error description",
    "status": 400,
    "timestamp": "2025-01-15T..."
  }
}
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage
```

Example test:

```typescript
describe('AuthController', () => {
  it('should send verification code', async () => {
    const response = await request(app)
      .post('/api/auth/send-code')
      .send({ email: '<EMAIL>' });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
  });
});
```

## 🗄️ Database Integration

### PostgreSQL with Prisma
```bash
npm install prisma @prisma/client
npx prisma init
```

### MongoDB with Mongoose
```bash
npm install mongoose
```

Update services to use real database instead of mock data.

## 🌍 Deployment

### Railway
```bash
npm install -g @railway/cli
railway login
railway init
railway up
```

### Heroku
```bash
heroku create your-app-name
git push heroku main
```

### DigitalOcean
1. Create App Platform application
2. Connect repository
3. Configure build settings

## 📈 Monitoring

Add logging and monitoring:

```typescript
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

## 🔄 API Versioning

Structure for API versioning:

```
routes/
├── v1/
│   ├── auth.ts
│   └── api.ts
└── v2/
    ├── auth.ts
    └── api.ts
```

## 🛡️ Security Best Practices

- ✅ Input validation
- ✅ Rate limiting
- ✅ CORS configuration
- ✅ Security headers (Helmet)
- ✅ JWT token validation
- ✅ Error handling
- ✅ Environment variables

## 📚 Learn More

- [Express.js](https://expressjs.com)
- [TypeScript](https://www.typescriptlang.org)
- [JWT](https://jwt.io)
- [Node.js](https://nodejs.org)