import express from 'express';
import { ApiController } from '../controllers/ApiController';
import { authMiddleware } from '../middleware/auth';

const router = express.Router();
const apiController = new ApiController();

// Public endpoints
router.get('/status', apiController.getStatus);

// Protected endpoints
router.get('/user/profile', authMiddleware, apiController.getUserProfile);
router.put('/user/profile', authMiddleware, apiController.updateUserProfile);

// Legacy endpoints - not used by frontend
// router.get('/posts', apiController.getPosts);
// router.get('/posts/:id', apiController.getPost);

export default router;