import express from 'express';
import { AuthController } from '../controllers/AuthController';
import { validateAuth } from '../middleware/validation';
import { authMiddleware } from '../middleware/auth';

const router = express.Router();
const authController = new AuthController();

// Public routes
router.post('/send-code', validateAuth.sendCode, authController.sendVerificationCode);
router.post('/verify', validateAuth.verify, authController.verifyCode);
router.post('/refresh', authController.refreshToken);

// Protected routes
router.get('/me', authMiddleware, authController.getCurrentUser);
router.post('/logout', authMiddleware, authController.logout);

export default router;