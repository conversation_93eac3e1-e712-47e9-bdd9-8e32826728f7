import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../utils/ApiResponse';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Validation middleware for auth endpoints
export const validateAuth = {
  sendCode: (req: Request, res: Response, next: NextFunction): void => {
    const { email } = req.body;

    if (!email) {
      res.status(400).json(ApiResponse.error('Email is required', 400));
      return;
    }

    if (!EMAIL_REGEX.test(email)) {
      res.status(400).json(ApiResponse.error('Invalid email format', 400));
      return;
    }

    next();
  },

  verify: (req: Request, res: Response, next: NextFunction): void => {
    const { email, code } = req.body;

    if (!email) {
      res.status(400).json(ApiResponse.error('Email is required', 400));
      return;
    }

    if (!EMAIL_REGEX.test(email)) {
      res.status(400).json(ApiResponse.error('Invalid email format', 400));
      return;
    }

    if (!code) {
      res.status(400).json(ApiResponse.error('Verification code is required', 400));
      return;
    }

    if (!/^\d{6}$/.test(code)) {
      res.status(400).json(ApiResponse.error('Verification code must be 6 digits', 400));
      return;
    }

    next();
  }
};

// General validation utilities
export const validate = {
  required: (field: string, value: any): string | null => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return `${field} is required`;
    }
    return null;
  },

  email: (email: string): string | null => {
    if (!EMAIL_REGEX.test(email)) {
      return 'Invalid email format';
    }
    return null;
  },

  minLength: (field: string, value: string, min: number): string | null => {
    if (value.length < min) {
      return `${field} must be at least ${min} characters long`;
    }
    return null;
  },

  maxLength: (field: string, value: string, max: number): string | null => {
    if (value.length > max) {
      return `${field} must not exceed ${max} characters`;
    }
    return null;
  },

  isNumeric: (field: string, value: string): string | null => {
    if (!/^\d+$/.test(value)) {
      return `${field} must contain only numbers`;
    }
    return null;
  }
};