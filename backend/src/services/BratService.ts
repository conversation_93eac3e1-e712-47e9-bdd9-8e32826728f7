export interface BratOptions {
  text: string;
  backgroundColor: string;
  textColor: string;
  fontSize: number;
  fontFamily?: string;
}

export class BratService {
  private readonly defaultColors = [
    { name: 'Brat Green', value: '#86c70a' },
    { name: 'Golden Yellow', value: '#c7910a' },
    { name: 'Burnt Orange', value: '#c7430a' },
    { name: 'Mint Green', value: '#0ac78e' },
    { name: 'Turquoise', value: '#0aa4c7' },
    { name: 'Electric Blue', value: '#0a5cc7' },
    { name: 'Purple Blue', value: '#490ac7' },
    { name: 'Hot Magenta', value: '#c10ac7' },
    { name: 'Cherry Red', value: '#c70a10' },
    { name: 'Pure White', value: '#ffffff' }
  ];

  async generateBratImage(options: BratOptions): Promise<Buffer> {
    const { text, backgroundColor, textColor, fontSize, fontFamily = 'Arial Black' } = options;
    
    try {
      // Generate square image - album cover style
      const size = 800; // Square dimensions
      
      // Keep original text case, don't force uppercase
      const displayText = text;
      
      // Break text into multiple lines if it's too long
      // More accurate calculation considering font width and padding
      const avgCharWidth = fontSize * 0.6; // More accurate for most fonts
      const padding = size * 0.1; // 10% padding on each side
      const availableWidth = size - (padding * 2);
      const maxCharsPerLine = Math.floor(availableWidth / avgCharWidth);
      const words = displayText.split(' ');
      const lines: string[] = [];
      let currentLine = '';
      
      for (const word of words) {
        if ((currentLine + ' ' + word).length <= maxCharsPerLine) {
          currentLine = currentLine ? currentLine + ' ' + word : word;
        } else {
          if (currentLine) {
            lines.push(currentLine);
            currentLine = word;
          } else {
            // Word is too long, break it
            lines.push(word);
          }
        }
      }
      if (currentLine) {
        lines.push(currentLine);
      }
      
      // Calculate line spacing and starting Y position
      const lineHeight = fontSize * 1.2;
      const totalHeight = lines.length * lineHeight;
      const minY = padding + fontSize; // Top boundary
      const maxY = size - padding; // Bottom boundary
      
      // Center vertically but ensure it doesn't exceed boundaries
      let startY = (size - totalHeight) / 2 + fontSize * 0.8;
      if (startY < minY) startY = minY;
      if (startY + totalHeight > maxY) startY = maxY - totalHeight;
      
      // Generate text elements for each line
      const textElements = lines.map((line, index) => {
        const y = startY + (index * lineHeight);
        return `<text x="50%" y="${y}" 
                      font-family="${fontFamily}, Arial, sans-serif" 
                      font-size="${fontSize}" 
                      font-weight="900" 
                      fill="${textColor}" 
                      text-anchor="middle" 
                      style="letter-spacing: 1px; text-rendering: optimizeLegibility; shape-rendering: geometricPrecision;">
          ${line}
        </text>`;
      }).join('\n');
      
      const svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${backgroundColor}"/>
  ${textElements}
</svg>`;
      
      return Buffer.from(svg, 'utf8');
    } catch (error) {
      console.error('Error in generateBratImage:', error);
      throw new Error(`Failed to generate image: ${error}`);
    }
  }

  getAvailableColors() {
    return this.defaultColors;
  }
}