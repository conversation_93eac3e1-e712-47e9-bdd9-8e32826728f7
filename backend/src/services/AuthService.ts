import jwt, { SignOptions } from 'jsonwebtoken';

interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: string;
}

interface AuthResult {
  success: boolean;
  token?: string;
  user?: User;
  message?: string;
}

export class AuthService {
  private readonly JWT_SECRET: string = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
  private readonly JWT_EXPIRES_IN: string = process.env.JWT_EXPIRES_IN || '24h';

  // Mock storage - replace with actual database
  private users: Map<string, User> = new Map();
  private verificationCodes: Map<string, { code: string; expiresAt: Date }> = new Map();
  private refreshTokens: Map<string, string> = new Map();

  public async sendVerificationCode(email: string): Promise<void> {
    // Generate 6-digit verification code
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store verification code
    this.verificationCodes.set(email, { code, expiresAt });

    // In production, send email with verification code
    console.log(`Verification code for ${email}: ${code}`);
    
    // Mock email sending - replace with actual email service
    // await this.emailService.sendVerificationCode(email, code);
  }

  public async verifyCode(email: string, code: string): Promise<AuthResult> {
    const storedData = this.verificationCodes.get(email);

    if (!storedData) {
      return { success: false, message: 'No verification code found' };
    }

    if (storedData.expiresAt < new Date()) {
      this.verificationCodes.delete(email);
      return { success: false, message: 'Verification code expired' };
    }

    if (storedData.code !== code) {
      return { success: false, message: 'Invalid verification code' };
    }

    // Remove used verification code
    this.verificationCodes.delete(email);

    // Get or create user
    let user = this.getUserByEmail(email);
    if (!user) {
      user = await this.createUser(email);
    }

    // Generate JWT token
    const token = this.generateToken(user);
    const refreshToken = this.generateRefreshToken(user);

    // Store refresh token
    this.refreshTokens.set(refreshToken, user.id);

    return {
      success: true,
      token,
      user
    };
  }

  public async refreshToken(refreshToken: string): Promise<AuthResult> {
    const userId = this.refreshTokens.get(refreshToken);

    if (!userId) {
      return { success: false, message: 'Invalid refresh token' };
    }

    const user = await this.getUserById(userId);
    if (!user) {
      this.refreshTokens.delete(refreshToken);
      return { success: false, message: 'User not found' };
    }

    // Generate new token
    const newToken = this.generateToken(user as User);

    return {
      success: true,
      token: newToken
    };
  }

  public async getUserById(id: string): Promise<User | null> {
    return this.users.get(id) || null;
  }

  public getUserByEmail(email: string): User | null {
    for (const user of this.users.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return null;
  }

  public async logout(userId: string): Promise<void> {
    // Remove all refresh tokens for this user
    for (const [token, id] of this.refreshTokens.entries()) {
      if (id === userId) {
        this.refreshTokens.delete(token);
      }
    }
  }

  public verifyToken(token: string): { id: string; email: string } | null {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      return {
        id: decoded.id,
        email: decoded.email
      };
    } catch (error) {
      return null;
    }
  }

  private async createUser(email: string): Promise<User> {
    const user: User = {
      id: this.generateUserId(),
      email,
      name: email.split('@')[0], // Use email prefix as default name
      createdAt: new Date().toISOString()
    };

    this.users.set(user.id, user);
    return user;
  }

  private generateToken(user: User): string {
    const options: SignOptions = { expiresIn: this.JWT_EXPIRES_IN as any };
    return jwt.sign(
      {
        id: user.id,
        email: user.email
      },
      this.JWT_SECRET,
      options
    );
  }

  private generateRefreshToken(user: User): string {
    const options: SignOptions = { expiresIn: '7d' };
    return jwt.sign(
      {
        id: user.id,
        type: 'refresh'
      },
      this.JWT_SECRET,
      options
    );
  }

  private generateUserId(): string {
    return 'user_' + Math.random().toString(36).substr(2, 9);
  }
}