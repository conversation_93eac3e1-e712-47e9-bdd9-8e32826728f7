export class ApiResponse {
  public static success(data?: any, message: string = 'Success'): object {
    return {
      success: true,
      message,
      data: data || null,
      timestamp: new Date().toISOString()
    };
  }

  public static error(message: string, status: number = 400, code?: string): object {
    return {
      success: false,
      error: {
        message,
        status,
        code: code || null,
        timestamp: new Date().toISOString()
      }
    };
  }

  public static created(data?: any, message: string = 'Created'): object {
    return {
      success: true,
      message,
      data: data || null,
      timestamp: new Date().toISOString()
    };
  }
}