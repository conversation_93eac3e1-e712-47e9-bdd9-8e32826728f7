import { Request, Response } from 'express';
import { BratService } from '../services/BratService';
import { ApiResponse } from '../utils/ApiResponse';

export class BratController {
  private bratService: BratService;

  constructor() {
    this.bratService = new BratService();
  }

  generateBrat = async (req: Request, res: Response): Promise<void> => {
    try {
      const { text, backgroundColor = '#8acf00', textColor = '#000000', fontSize = 48, fontFamily = 'Arial Black' } = req.body;

      if (!text || text.trim().length === 0) {
        res.status(400).json(ApiResponse.error('Text is required'));
        return;
      }

      if (text.length > 100) {
        res.status(400).json(ApiResponse.error('Text must be 100 characters or less'));
        return;
      }

      const imageBuffer = await this.bratService.generateBratImage({
        text: text.trim(),
        backgroundColor,
        textColor,
        fontSize: parseInt(fontSize.toString()),
        fontFamily
      });

      res.set({
        'Content-Type': 'image/svg+xml',
        'Content-Disposition': 'inline; filename="brat.svg"',
        'Cache-Control': 'public, max-age=3600'
      });

      res.send(imageBuffer);
    } catch (error) {
      console.error('Error generating brat:', error);
      res.status(500).json(ApiResponse.error('Failed to generate brat image'));
    }
  };

  getColors = async (_req: Request, res: Response): Promise<void> => {
    try {
      const colors = this.bratService.getAvailableColors();
      res.json(ApiResponse.success(colors, 'Available colors retrieved successfully'));
    } catch (error) {
      console.error('Error getting colors:', error);
      res.status(500).json(ApiResponse.error('Failed to get available colors'));
    }
  };
}