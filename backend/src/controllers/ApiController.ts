import { Request, Response } from 'express';
import { ApiResponse } from '../utils/ApiResponse';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
  };
}

export class ApiController {
  public getStatus = async (_req: Request, res: Response): Promise<void> => {
    try {
      res.status(200).json(ApiResponse.success({
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      }, 'API is running'));
    } catch (error) {
      console.error('Get status error:', error);
      res.status(500).json(ApiResponse.error('Failed to get status', 500));
    }
  };

  public getUserProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json(ApiResponse.error('User not authenticated', 401));
        return;
      }

      // Mock user profile data - replace with actual database call
      const userProfile = {
        id: req.user.id,
        email: req.user.email,
        name: '<PERSON>',
        avatar: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      res.status(200).json(ApiResponse.success({ profile: userProfile }, 'User profile retrieved successfully'));
    } catch (error) {
      console.error('Get user profile error:', error);
      res.status(500).json(ApiResponse.error('Failed to get user profile', 500));
    }
  };

  public updateUserProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json(ApiResponse.error('User not authenticated', 401));
        return;
      }

      const { name, avatar } = req.body;

      // Mock profile update - replace with actual database call
      const updatedProfile = {
        id: req.user.id,
        email: req.user.email,
        name: name || 'John Doe',
        avatar: avatar || null,
        updatedAt: new Date().toISOString()
      };

      res.status(200).json(ApiResponse.success({ profile: updatedProfile }, 'User profile updated successfully'));
    } catch (error) {
      console.error('Update user profile error:', error);
      res.status(500).json(ApiResponse.error('Failed to update user profile', 500));
    }
  };

  // Legacy methods - not used by frontend blog system
  /*
  public getPosts = async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      // Mock posts data - replace with actual database call
      const posts = Array.from({ length: limit }, (_, index) => ({
        id: `post-${page}-${index + 1}`,
        title: `Sample Post ${(page - 1) * limit + index + 1}`,
        content: 'This is a sample post content. Replace with actual content from your database.',
        author: 'Template Author',
        createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
        updatedAt: new Date().toISOString(),
        published: true
      }));

      const totalPosts = 100; // Mock total count
      const totalPages = Math.ceil(totalPosts / limit);

      res.status(200).json(ApiResponse.success({
        posts,
        pagination: {
          page,
          limit,
          totalPosts,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }, 'Posts retrieved successfully'));
    } catch (error) {
      console.error('Get posts error:', error);
      res.status(500).json(ApiResponse.error('Failed to get posts', 500));
    }
  };

  public getPost = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json(ApiResponse.error('Post ID is required', 400));
        return;
      }

      // Mock post data - replace with actual database call
      const post = {
        id,
        title: `Sample Post ${id}`,
        content: 'This is a detailed sample post content. Replace with actual content from your database.',
        author: 'Template Author',
        tags: ['sample', 'template', 'api'],
        createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
        updatedAt: new Date().toISOString(),
        published: true,
        views: Math.floor(Math.random() * 1000),
        likes: Math.floor(Math.random() * 100)
      };

      res.status(200).json(ApiResponse.success({ post }, 'Post retrieved successfully'));
    } catch (error) {
      console.error('Get post error:', error);
      res.status(500).json(ApiResponse.error('Failed to get post', 500));
    }
  };
  */
}