import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService';
import { ApiResponse } from '../utils/ApiResponse';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
  };
}

export class AuthController {
  private authService = new AuthService();

  public sendVerificationCode = async (req: Request, res: Response): Promise<void> => {
    try {
      const { email } = req.body;
      
      await this.authService.sendVerificationCode(email);
      
      res.json(ApiResponse.success({
        message: 'Please check your email for the verification code'
      }, 'Verification code sent successfully'));
    } catch (error) {
      console.error('Send verification code error:', error);
      res.status(500).json(ApiResponse.error('Failed to send verification code', 500));
    }
  };

  public verifyCode = async (req: Request, res: Response): Promise<void> => {
    try {
      const { email, code } = req.body;
      
      const result = await this.authService.verifyCode(email, code);
      
      if (result.success) {
        res.json(ApiResponse.success({
          token: result.token,
          user: result.user,
          expiresIn: '24h'
        }, 'Authentication successful'));
      } else {
        res.status(400).json(ApiResponse.error('Invalid verification code', 400));
      }
    } catch (error) {
      console.error('Verify code error:', error);
      res.status(500).json(ApiResponse.error('Authentication failed', 500));
    }
  };

  public refreshToken = async (req: Request, res: Response): Promise<void> => {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        res.status(400).json(ApiResponse.error('Refresh token is required', 400));
        return;
      }

      const result = await this.authService.refreshToken(refreshToken);
      
      if (result.success) {
        res.json(ApiResponse.success({
          token: result.token,
          expiresIn: '24h'
        }, 'Token refreshed successfully'));
      } else {
        res.status(401).json(ApiResponse.error('Invalid refresh token', 401));
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json(ApiResponse.error('Token refresh failed', 500));
    }
  };

  public getCurrentUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json(ApiResponse.error('User not authenticated', 401));
        return;
      }

      const user = await this.authService.getUserById(req.user.id);
      
      if (user) {
        res.json(ApiResponse.success({ user }, 'User retrieved successfully'));
      } else {
        res.status(404).json(ApiResponse.error('User not found', 404));
      }
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json(ApiResponse.error('Failed to get user information', 500));
    }
  };

  public logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json(ApiResponse.error('User not authenticated', 401));
        return;
      }

      await this.authService.logout(req.user.id);
      
      res.json(ApiResponse.success(undefined, 'Logged out successfully'));
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json(ApiResponse.error('Logout failed', 500));
    }
  };
}