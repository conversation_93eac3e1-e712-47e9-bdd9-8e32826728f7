# Server Configuration
PORT=3001
NODE_ENV=development

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Database Configuration (if using a database)
# DATABASE_URL=postgresql://username:password@localhost:5432/template_db
# MONGODB_URI=mongodb://localhost:27017/template_db

# Email Configuration (for sending verification codes)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-app-password

# Redis Configuration (for caching and sessions)
# REDIS_URL=redis://localhost:6379

# File Upload Configuration
# UPLOAD_MAX_SIZE=10485760
# UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# External API Keys
# THIRD_PARTY_API_KEY=your-api-key-here

# Logging
# LOG_LEVEL=info
# LOG_FILE=logs/app.log

# Rate Limiting
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100