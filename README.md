# Template App - Full Stack Web Application

A modern, full-stack web application template built with Next.js, TypeScript, and Node.js. This template provides a solid foundation for building scalable web applications with a beautiful frontend and robust backend.

## 🚀 Features

### Frontend (Next.js 15)
- **Modern UI**: Beautiful, responsive design with Tailwind CSS
- **Smooth Animations**: Custom scroll animations and micro-interactions
- **TypeScript**: Full type safety throughout the application
- **Performance Optimized**: Built with Next.js for optimal performance
- **Mobile Responsive**: Works perfectly on all device sizes
- **SEO Ready**: Built-in SEO optimization

### Backend (Node.js + Express)
- **RESTful API**: Clean, well-structured API endpoints
- **Authentication**: JWT-based authentication with email verification
- **Security**: Helmet, CORS, rate limiting, and input validation
- **Error Handling**: Comprehensive error handling and logging
- **TypeScript**: Full type safety on the backend
- **Scalable Architecture**: Clean separation of concerns

## 📁 Project Structure

```
template/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── app/             # Next.js App Router
│   │   │   ├── page.tsx     # Homepage
│   │   │   ├── auth/        # Authentication pages
│   │   │   ├── blog/        # Blog pages
│   │   │   └── globals.css  # Global styles
│   │   ├── components/      # Reusable React components
│   │   └── lib/            # Utility libraries
│   ├── package.json
│   ├── tailwind.config.ts
│   └── next.config.ts
│
├── backend/                  # Node.js backend API
│   ├── src/
│   │   ├── routes/          # API routes
│   │   ├── controllers/     # Request handlers
│   │   ├── services/        # Business logic
│   │   ├── middleware/      # Express middleware
│   │   ├── utils/           # Utility functions
│   │   └── index.ts         # Server entry point
│   ├── package.json
│   └── tsconfig.json
│
└── README.md                # This file
```

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety and better developer experience
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **Custom Animations** - Smooth scroll reveals and transitions

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **TypeScript** - Type safety for backend code
- **JWT** - JSON Web Tokens for authentication
- **bcryptjs** - Password hashing
- **Helmet** - Security middleware
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - Request rate limiting

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

### Installation

1. **Clone or copy the template:**
   ```bash
   # Copy the template directory to your project location
   cp -r template/ my-new-project/
   cd my-new-project/
   ```

2. **Install Frontend Dependencies:**
   ```bash
   cd frontend/
   npm install
   ```

3. **Install Backend Dependencies:**
   ```bash
   cd ../backend/
   npm install
   ```

4. **Set up Environment Variables:**
   ```bash
   # In the backend directory
   cp .env.example .env
   
   # Edit .env with your configuration
   nano .env
   ```

### Running the Application

1. **Start the Backend Server:**
   ```bash
   cd backend/
   npm run dev
   ```
   The API will be available at `http://localhost:3001`

2. **Start the Frontend Application:**
   ```bash
   cd frontend/
   npm run dev
   ```
   The website will be available at `http://localhost:3000`

## 📚 Available Scripts

### Frontend Scripts
```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
```

### Backend Scripts
```bash
npm run dev          # Start development server with nodemon
npm run build        # Build TypeScript to JavaScript
npm start            # Start production server
npm run lint         # Run ESLint
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
```

## 🎨 Customization

### Branding
1. **Update the app name and logo:**
   - Change "Template App" in `frontend/src/app/page.tsx`
   - Replace the heart icon with your logo
   - Update colors in `tailwind.config.ts`

2. **Customize the color scheme:**
   - Edit the gradient colors in the components
   - Update Tailwind CSS configuration

### Content
1. **Homepage content:**
   - Edit `frontend/src/app/page.tsx`
   - Update hero section, features, and footer

2. **Blog posts:**
   - Modify the mock data in `frontend/src/app/blog/page.tsx`
   - Connect to your CMS or database

3. **API endpoints:**
   - Add new routes in `backend/src/routes/`
   - Implement business logic in `backend/src/services/`

## 🔐 Authentication

The template includes a complete authentication system:

- **Email verification**: Users sign in with email verification codes
- **JWT tokens**: Secure token-based authentication
- **Protected routes**: Middleware for protecting API endpoints
- **Refresh tokens**: Long-term authentication management

### Adding New Protected Routes

```typescript
// In your route file
import { authMiddleware } from '../middleware/auth';

router.get('/protected-endpoint', authMiddleware, controller.method);
```

## 🌍 Deployment

### Frontend (Vercel - Recommended)
1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on git push

### Backend (Various Options)

**Railway:**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

**Heroku:**
```bash
# Install Heroku CLI and login
heroku create your-app-name
git push heroku main
```

**DigitalOcean App Platform:**
1. Connect your repository
2. Configure build and run commands
3. Set environment variables

## 📝 Environment Variables

### Backend (.env)
```env
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com
JWT_SECRET=your-super-secret-key
JWT_EXPIRES_IN=24h
```

### Frontend (Vercel Environment Variables)
```env
NEXT_PUBLIC_API_URL=https://your-backend-domain.com
```

## 🧪 Testing

The template includes testing setup for both frontend and backend:

```bash
# Run frontend tests
cd frontend && npm test

# Run backend tests
cd backend && npm test

# Run tests with coverage
npm run test:coverage
```

## 🔧 Database Integration

The template uses mock data for quick setup. To integrate a real database:

1. **PostgreSQL with Prisma:**
   ```bash
   npm install prisma @prisma/client
   npx prisma init
   ```

2. **MongoDB with Mongoose:**
   ```bash
   npm install mongoose
   ```

3. **Update the services** to use your database instead of mock data

## 📖 API Documentation

### Authentication Endpoints
- `POST /api/auth/send-code` - Send verification code to email
- `POST /api/auth/verify` - Verify code and get JWT token
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/me` - Get current user (protected)
- `POST /api/auth/logout` - Logout user (protected)

### General Endpoints
- `GET /api/status` - API status
- `GET /api/posts` - Get paginated posts
- `GET /api/posts/:id` - Get specific post
- `GET /api/user/profile` - Get user profile (protected)
- `PUT /api/user/profile` - Update user profile (protected)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you have any questions or need help with the template:

1. Check the documentation above
2. Look at the example code in the template
3. Create an issue in the repository
4. Contact the development team

## 🎯 Roadmap

Future enhancements for this template:

- [ ] Database integration examples (PostgreSQL, MongoDB)
- [ ] Real-time features with WebSockets
- [ ] Email service integration
- [ ] File upload functionality
- [ ] Admin dashboard
- [ ] Advanced authentication (OAuth, 2FA)
- [ ] Performance monitoring
- [ ] Docker containerization
- [ ] CI/CD pipeline examples

---

Built with ❤️ using modern web technologies. Happy coding!