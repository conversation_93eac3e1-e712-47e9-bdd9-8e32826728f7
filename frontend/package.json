{"name": "template-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "lucide-react": "^0.462.0", "next": "15.0.3", "react": "19.0.0", "react-dom": "19.0.0", "tailwindcss": "^3.4.1", "typescript": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.0.0", "autoprefixer": "^10.4.14", "eslint": "^8", "eslint-config-next": "15.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8"}}