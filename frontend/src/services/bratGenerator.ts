export interface BratOptions {
  text: string;
  backgroundColor: string;
  textColor: string;
  fontSize: number;
  fontFamily?: string;
  autoFontSize?: boolean; // Whether to use intelligent font sizing
}

export class BratGenerator {
  private static readonly defaultColors = [
    { name: '<PERSON><PERSON> <PERSON>', value: '#86c70a' },
    { name: 'Golden Yellow', value: '#c7910a' },
    { name: 'Burnt Orange', value: '#c7430a' },
    { name: 'Mint Green', value: '#0ac78e' },
    { name: 'Turquoise', value: '#0aa4c7' },
    { name: 'Electric Blue', value: '#0a5cc7' },
    { name: 'Purple Blue', value: '#490ac7' },
    { name: 'Hot Magenta', value: '#c10ac7' },
    { name: 'Cherry Red', value: '#c70a10' },
    { name: 'Pure White', value: '#ffffff' }
  ];

  /**
   * Calculate optimal font size and layout to fit text within canvas
   * Mimics competitor behavior: tries to fit text optimally within canvas bounds
   */
  static calculateOptimalLayout(text: string, canvasSize: number = 800): { fontSize: number, lineBreaks: string[] } {
    if (!text || text.trim().length === 0) {
      return { fontSize: 48, lineBreaks: [] };
    }

    const cleanText = text.trim();
    const padding = canvasSize * 0.15; // 15% padding
    const availableWidth = canvasSize - (padding * 2);
    const availableHeight = canvasSize - (padding * 2);
    
    // Start with a large font size and work down
    let optimalFontSize = 120;
    let bestLayout: string[] = [];
    
    // Try different font sizes from large to small
    for (let fontSize = 140; fontSize >= 18; fontSize -= 3) {
      const layout = this.calculateTextLayout(cleanText, fontSize, availableWidth);
      const lineHeight = fontSize * 1.15; // Slightly tighter line height
      const totalHeight = layout.length * lineHeight;
      
      // Check if layout fits within available height
      if (totalHeight <= availableHeight) {
        // More accurate width checking using better character width estimation
        const allLinesFit = layout.every(line => {
          // Better character width estimation based on typical brat font characteristics
          const estimatedWidth = this.estimateTextWidth(line, fontSize);
          return estimatedWidth <= availableWidth;
        });
        
        if (allLinesFit) {
          optimalFontSize = fontSize;
          bestLayout = layout;
          break;
        }
      }
    }
    
    // If still no good fit, use minimum size and force fit
    if (bestLayout.length === 0) {
      optimalFontSize = 18;
      bestLayout = this.calculateTextLayout(cleanText, optimalFontSize, availableWidth);
    }
    
    return { fontSize: optimalFontSize, lineBreaks: bestLayout };
  }

  /**
   * Estimate text width more accurately for bold fonts typically used in brat covers
   */
  private static estimateTextWidth(text: string, fontSize: number): number {
    // Character width ratios for Arial Light with horizontal scale(0.85, 1) transform
    const charWidthMap: { [key: string]: number } = {
      // Wide characters (scaled 0.85x)
      'w': 0.61, 'm': 0.61, 'W': 0.66, 'M': 0.66,
      // Medium characters (scaled 0.85x)
      'a': 0.37, 'b': 0.37, 'c': 0.33, 'd': 0.37, 'e': 0.33, 'f': 0.21, 'g': 0.37, 'h': 0.37,
      'n': 0.37, 'o': 0.37, 'p': 0.37, 'q': 0.37, 'r': 0.22, 's': 0.31, 't': 0.22, 'u': 0.37,
      'v': 0.33, 'x': 0.33, 'y': 0.33, 'z': 0.31,
      // Uppercase (scaled 0.85x)
      'A': 0.45, 'B': 0.41, 'C': 0.45, 'D': 0.45, 'E': 0.37, 'F': 0.37, 'G': 0.45, 'H': 0.45,
      'I': 0.15, 'J': 0.28, 'K': 0.41, 'L': 0.33, 'N': 0.45, 'O': 0.49, 'P': 0.37, 'Q': 0.49,
      'R': 0.41, 'S': 0.37, 'T': 0.37, 'U': 0.45, 'V': 0.41, 'X': 0.41, 'Y': 0.41, 'Z': 0.37,
      // Numbers (scaled 0.85x)
      '0': 0.37, '1': 0.21, '2': 0.37, '3': 0.37, '4': 0.37, '5': 0.37, 
      '6': 0.37, '7': 0.37, '8': 0.37, '9': 0.37,
      // Common punctuation (scaled 0.85x)
      ' ': 0.15, '!': 0.15, '?': 0.33, '.': 0.15, ',': 0.15, ':': 0.15, ';': 0.15,
      '-': 0.21, '_': 0.28, '(': 0.21, ')': 0.21, '[': 0.17, ']': 0.17, '{': 0.21, '}': 0.21
    };
    
    let totalWidth = 0;
    for (const char of text) {
      const charWidth = charWidthMap[char] || 0.37; // Default width for unknown characters
      totalWidth += charWidth * fontSize;
    }
    
    return totalWidth;
  }

  /**
   * Calculate how text should be broken into lines for a given font size
   * More sophisticated algorithm that tries to balance line lengths
   */
  private static calculateTextLayout(text: string, fontSize: number, availableWidth: number): string[] {
    // Handle very short text - keep on single line if possible
    if (this.estimateTextWidth(text, fontSize) <= availableWidth) {
      return [text];
    }
    
    // Split into words for intelligent line breaking
    const words = text.split(/\s+/);
    const lines: string[] = [];
    let currentLine = '';
    
    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = this.estimateTextWidth(testLine, fontSize);
      
      // Check if adding this word would exceed the available width
      if (testWidth <= availableWidth) {
        currentLine = testLine;
      } else {
        // If current line has content, save it and start new line
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, need to break it
          const wordWidth = this.estimateTextWidth(word, fontSize);
          if (wordWidth > availableWidth) {
            // Break long word character by character
            let remainingWord = word;
            while (remainingWord.length > 0) {
              let chunk = '';
              for (let i = 1; i <= remainingWord.length; i++) {
                const testChunk = remainingWord.substring(0, i);
                if (this.estimateTextWidth(testChunk, fontSize) <= availableWidth) {
                  chunk = testChunk;
                } else {
                  break;
                }
              }
              if (chunk === '') {
                // Even single character is too wide, force it
                chunk = remainingWord.substring(0, 1);
              }
              lines.push(chunk);
              remainingWord = remainingWord.substring(chunk.length);
            }
            currentLine = '';
          } else {
            currentLine = word;
          }
        }
      }
    }
    
    // Add remaining content
    if (currentLine) {
      lines.push(currentLine);
    }
    
    return lines;
  }

  /**
   * Backward compatibility method
   */
  static calculateIntelligentFontSize(text: string): number {
    const layout = this.calculateOptimalLayout(text);
    return layout.fontSize;
  }

  static generateSVG(options: BratOptions): string {
    const { text, backgroundColor, textColor, fontFamily = 'Arial', autoFontSize = false } = options;
    
    // Generate square image - album cover style
    const size = 800; // Square dimensions
    
    // Keep original text case, don't force uppercase
    const displayText = text;
    
    let finalFontSize: number;
    let lines: string[];
    
    if (autoFontSize) {
      // Use new optimal layout algorithm
      const layout = this.calculateOptimalLayout(displayText, size);
      finalFontSize = layout.fontSize;
      lines = layout.lineBreaks;
    } else {
      // Use provided font size with simple line breaking
      finalFontSize = options.fontSize;
      const padding = size * 0.15; // 15% padding
      const availableWidth = size - (padding * 2);
      lines = this.calculateTextLayout(displayText, finalFontSize, availableWidth);
    }
    
    // Calculate line spacing and positioning
    const lineHeight = finalFontSize * 1.15; // Match the layout calculation
    const totalHeight = lines.length * lineHeight;
    const padding = size * 0.15; // 15% padding on each side
    
    // Center vertically within the canvas
    const startY = (size - totalHeight) / 2 + finalFontSize * 0.75;
    
    // Generate text elements for each line
    const textElements = lines.map((line, index) => {
      const y = startY + (index * lineHeight);
      const centerX = size / 2; // Calculate center position
      return `<text x="${centerX}" y="${y}" 
                    font-family="${fontFamily}, Arial, sans-serif" 
                    font-size="${finalFontSize}" 
                    font-weight="300" 
                    fill="${textColor}" 
                    text-anchor="middle" 
                    transform="scale(0.85, 1) translate(${centerX * 0.176}, 0)"
                    style="letter-spacing: 0.5px; text-rendering: optimizeLegibility; shape-rendering: geometricPrecision;">
        ${this.escapeXml(line)}
      </text>`;
    }).join('\n');
    
    const svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${backgroundColor}"/>
  ${textElements}
</svg>`;
    
    return svg;
  }

  static async generatePNG(options: BratOptions): Promise<string> {
    const svgText = this.generateSVG(options);
    return this.convertSVGToPNG(svgText);
  }

  static convertSVGToPNG(svgText: string): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const parser = new DOMParser();
        const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
        const svgElement = svgDoc.querySelector('svg');
        
        if (!svgElement) {
          throw new Error('Invalid SVG');
        }
        
        const width = parseInt(svgElement.getAttribute('width') || '800');
        const height = parseInt(svgElement.getAttribute('height') || '800');
        
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = width;
        canvas.height = height;
        
        if (ctx) {
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
        }
        
        const img = new window.Image();
        
        img.onload = () => {
          if (ctx) {
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, width, height);
            ctx.drawImage(img, 0, 0, width, height);
            
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                resolve(url);
              } else {
                reject(new Error('Failed to create blob'));
              }
            }, 'image/png', 1.0);
          }
          
          URL.revokeObjectURL(img.src);
        };
        
        img.onerror = () => {
          reject(new Error('Failed to load SVG image'));
        };
        
        const blob = new Blob([svgText], { type: 'image/svg+xml' });
        img.src = URL.createObjectURL(blob);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  static downloadPNG(options: BratOptions): void {
    try {
      const svgText = this.generateSVG(options);
      this.convertSVGToPNGAndDownload(svgText);
    } catch (error) {
      console.error('Error generating brat cover:', error);
      alert('Failed to generate image. Please try again.');
    }
  }

  private static convertSVGToPNGAndDownload(svgText: string): void {
    try {
      // Parse SVG dimensions
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(svgText, 'image/svg+xml');
      const svgElement = svgDoc.querySelector('svg');
      
      if (!svgElement) {
        throw new Error('Invalid SVG');
      }
      
      const width = parseInt(svgElement.getAttribute('width') || '800');
      const height = parseInt(svgElement.getAttribute('height') || '800');
      
      // Create canvas with better rendering settings
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = width;
      canvas.height = height;
      
      // Improve rendering quality
      if (ctx) {
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        (ctx as any).textRendering = 'optimizeLegibility';
      }
      
      // Create image from SVG
      const img = new window.Image();
      
      img.onload = () => {
        // Draw white background first
        if (ctx) {
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, width, height);
          
          // Draw the SVG image
          ctx.drawImage(img, 0, 0, width, height);
          
          // Convert to PNG and download
          canvas.toBlob((blob) => {
            if (blob) {
              const link = document.createElement('a');
              link.href = URL.createObjectURL(blob);
              link.download = 'brat.png';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(link.href);
            }
          }, 'image/png', 1.0);
        }
        
        // Clean up
        URL.revokeObjectURL(img.src);
      };
      
      img.onerror = (error) => {
        console.error('Error loading SVG:', error);
        alert('Failed to convert image. Please try again.');
      };
      
      // Convert SVG to data URL
      const svgBlob = new Blob([svgText], { type: 'image/svg+xml;charset=utf-8' });
      img.src = URL.createObjectURL(svgBlob);
      
    } catch (error) {
      console.error('Error converting SVG to PNG:', error);
      alert('Failed to process image. Please try again.');
    }
  }

  private static escapeXml(unsafe: string): string {
    return unsafe.replace(/[<>&'"]/g, (c) => {
      switch (c) {
        case '<': return '&lt;';
        case '>': return '&gt;';
        case '&': return '&amp;';
        case '\'': return '&#39;';
        case '"': return '&quot;';
        default: return c;
      }
    });
  }

  static getAvailableColors() {
    return this.defaultColors;
  }
}