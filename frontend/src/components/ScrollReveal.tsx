'use client';

import { useEffect, useRef, ReactNode } from 'react';

interface ScrollRevealProps {
  children: ReactNode;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  className?: string;
}

export default function ScrollReveal({ 
  children, 
  delay = 0, 
  direction = 'up', 
  distance = 30,
  className = '' 
}: ScrollRevealProps) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            setTimeout(() => {
              element.style.opacity = '1';
              element.style.transform = 'translate3d(0, 0, 0)';
            }, delay);
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    );

    const element = elementRef.current;
    if (element) {
      // Set initial styles
      const getInitialTransform = () => {
        switch (direction) {
          case 'up':
            return `translate3d(0, ${distance}px, 0)`;
          case 'down':
            return `translate3d(0, -${distance}px, 0)`;
          case 'left':
            return `translate3d(${distance}px, 0, 0)`;
          case 'right':
            return `translate3d(-${distance}px, 0, 0)`;
          default:
            return `translate3d(0, ${distance}px, 0)`;
        }
      };

      element.style.opacity = '0';
      element.style.transform = getInitialTransform();
      element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [delay, direction, distance]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
}