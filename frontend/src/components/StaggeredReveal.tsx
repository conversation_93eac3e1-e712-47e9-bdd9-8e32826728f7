'use client';

import { useEffect, useRef, ReactNode } from 'react';

interface StaggeredRevealProps {
  children: ReactNode;
  staggerDelay?: number;
  animation?: 'fadeInUp' | 'scaleIn' | 'slideInLeft' | 'slideInRight' | 'fadeIn';
  className?: string;
}

export default function StaggeredReveal({ 
  children, 
  staggerDelay = 100, 
  animation = 'fadeInUp',
  className = '' 
}: StaggeredRevealProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const container = entry.target as HTMLElement;
            const childElements = container.children;

            Array.from(childElements).forEach((child, index) => {
              const element = child as HTMLElement;
              setTimeout(() => {
                element.classList.add(`animate-${animation}`);
                element.style.opacity = '1';
              }, index * staggerDelay);
            });
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    );

    const container = containerRef.current;
    if (container) {
      // Set initial styles for all children
      Array.from(container.children).forEach((child) => {
        const element = child as HTMLElement;
        element.style.opacity = '0';
      });

      observer.observe(container);
    }

    return () => {
      if (container) {
        observer.unobserve(container);
      }
    };
  }, [staggerDelay, animation]);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}