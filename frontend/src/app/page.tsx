'use client';

import { useState, useEffect, useCallback } from 'react';
import { Download, Palette, Type, Sparkles, Coffee, Heart, ArrowRight, CheckCircle, Star, Users, Clock, Shield, ChevronDown, ChevronUp, HelpCircle } from 'lucide-react';
import ScrollReveal from '@/components/ScrollReveal';
import Navbar from '@/components/Navbar';
import { BratGenerator } from '@/services/bratGenerator';

export default function HomePage() {
  const [text, setText] = useState('brat');
  const [backgroundColor, setBackgroundColor] = useState('#8acf00');
  const [textColor, setTextColor] = useState('#000000');
  const [fontSize, setFontSize] = useState(48);
  const [fontFamily, setFontFamily] = useState('Arial');
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const [useAutoFontSize, setUseAutoFontSize] = useState(true); // Enable auto font size by default
  const [blurIntensity, setBlurIntensity] = useState(0); // Blur intensity (0-5px)

  const defaultColors = [
    { name: 'Brat Green', value: '#86c70a' },
    { name: 'Golden Yellow', value: '#fad54f' },
    { name: 'Burnt Orange', value: '#fa7747' },
    { name: 'Mint Green', value: '#5effc6' },
    { name: 'Turquoise', value: '#31bfbf' },
    { name: 'Electric Blue', value: '#0a13c7' },
    { name: 'Purple Blue', value: '#8d69d1' },
    { name: 'Hot Magenta', value: '#c27acd' },
    { name: 'Cherry Red', value: '#8c070b' },
    { name: 'Pure White', value: '#ffffff' }
  ];

  const fontOptions = [
    'Arial',
    'Helvetica',
    'Helvetica Neue',
    'Impact',
    'Verdana',
    'Trebuchet MS',
    'Times New Roman',
    'Georgia',
    'Comic Sans MS',
    'Courier New'
  ];

  // Get current effective font size for display
  const getEffectiveFontSize = useCallback(() => {
    if (useAutoFontSize) {
      return BratGenerator.calculateIntelligentFontSize(text);
    }
    return fontSize;
  }, [text, fontSize, useAutoFontSize]);

  // Generate preview PNG using frontend-only implementation
  const generatePreviewPNG = useCallback(async () => {
    const effectiveFontSize = getEffectiveFontSize();
    console.log('Generating preview PNG...', { text, backgroundColor, fontSize: effectiveFontSize, fontFamily, useAutoFontSize, blurIntensity });
    try {
      const pngUrl = await BratGenerator.generatePNG({
        text,
        backgroundColor,
        textColor,
        fontSize,
        fontFamily,
        autoFontSize: useAutoFontSize,
        blurIntensity
      });
      console.log('PNG generated, setting preview URL');
      setPreviewImageUrl(pngUrl);
    } catch (error) {
      console.error('Error generating preview:', error);
    }
  }, [text, backgroundColor, textColor, fontSize, fontFamily, useAutoFontSize, blurIntensity, getEffectiveFontSize]);

  // Auto-generate preview when parameters change
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (text.trim()) {
        generatePreviewPNG();
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(debounceTimer);
  }, [text, backgroundColor, textColor, fontSize, fontFamily, useAutoFontSize, blurIntensity, generatePreviewPNG]);

  // Generate initial preview on mount
  useEffect(() => {
    // Use timeout to ensure component is fully mounted
    const timer = setTimeout(() => {
      generatePreviewPNG();
    }, 100);

    return () => clearTimeout(timer);
  }, [generatePreviewPNG]); // Depends on generatePreviewPNG


  const generateBrat = async () => {
    if (!text.trim()) return;

    setIsGenerating(true);
    try {
      BratGenerator.downloadPNG({
        text,
        backgroundColor,
        textColor,
        fontSize,
        fontFamily,
        autoFontSize: useAutoFontSize,
        blurIntensity
      });
    } catch (error) {
      console.error('Error generating brat:', error);
      alert('Failed to generate image. Please try again.');
    }
    setIsGenerating(false);
  };


  const faqData = [
    {
      question: "How do I download my brat cover?",
      answer: "Simply enter your text, customize colors and fonts, then click the 'Download' button. Your brat cover will be saved as a PNG image to your device."
    },
    {
      question: "What image formats are supported?",
      answer: "Currently, we generate high-quality PNG images (800x800 pixels) that are perfect for social media, album covers, and printing."
    },
    {
      question: "Can I use custom fonts?",
      answer: "Yes! We offer a selection of popular fonts including Arial, Impact, Times New Roman, and more. Choose from the font dropdown to customize your brat cover."
    },
    {
      question: "Is there a character limit for text?",
      answer: "Yes, we have a 100-character limit to ensure optimal readability and design. The tool will automatically wrap long text across multiple lines."
    },
    {
      question: "Are the images free to use?",
      answer: "Absolutely! All generated brat covers are free to use for personal and commercial purposes. You own the rights to your created content."
    },
    {
      question: "Why isn't my preview showing?",
      answer: "If the preview isn't loading, try refreshing the page or checking your internet connection. The preview generates automatically as you type."
    },
    {
      question: "Can I save my color preferences?",
      answer: "Your color and font preferences are temporarily saved during your session. We're working on adding permanent preference saving in a future update."
    },
    {
      question: "How do I report a bug or suggest a feature?",
      answer: "We'd love to hear from you! Contact <NAME_EMAIL> with any bugs, suggestions, or feedback."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50">
      <Navbar />

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        <ScrollReveal>
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              <span className="bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent">
                Brat Generator
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Brat Generator is an innovative online tool that helps you turn your ideas into bold, eye-catching brat-style text covers quickly. Create custom covers with personalized text, colors, and styles instantly.
            </p>
          </div>
        </ScrollReveal>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Generator Interface */}
          <ScrollReveal delay={200}>
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-green-100 shadow-xl">
              <h2 className="text-2xl font-bold mb-6 flex items-center text-black">
                <Type className="w-6 h-6 mr-2 text-green-600" />
                Customize Your Brat
              </h2>

              {/* Text Input */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Your Text ↓
                </label>
                <input
                  type="text"
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent text-black"
                  placeholder="Enter your text here..."
                  maxLength={100}
                />
                <p className="text-sm text-gray-700 mt-1">{text.length}/100 characters</p>
              </div>

              {/* Background Color */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Background Color
                </label>
                <div className="grid grid-cols-5 gap-2 mb-3">
                  {defaultColors.map((color) => (
                    <button
                      key={color.value}
                      onClick={() => setBackgroundColor(color.value)}
                      className={`w-12 h-12 rounded-lg border-2 ${
                        backgroundColor === color.value ? 'border-gray-400' : 'border-gray-200'
                      } hover:border-gray-400 transition-colors`}
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
                <input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="w-full h-12 rounded-lg border border-gray-300 cursor-pointer"
                />
              </div>

              {/* Text Color */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Text Color
                </label>
                <input
                  type="color"
                  value={textColor}
                  onChange={(e) => setTextColor(e.target.value)}
                  className="w-full h-12 rounded-lg border border-gray-300 cursor-pointer"
                />
              </div>

              {/* Font Family */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Font Family
                </label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent text-black bg-white"
                >
                  {fontOptions.map((font) => (
                    <option key={font} value={font} style={{ fontFamily: font }}>
                      {font}
                    </option>
                  ))}
                </select>
              </div>

              {/* Blur Intensity */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Text Blur Effect: {blurIntensity}px
                </label>
                <input
                  type="range"
                  min="0"
                  max="5"
                  step="0.5"
                  value={blurIntensity}
                  onChange={(e) => setBlurIntensity(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-sm text-gray-600 mt-1">
                  <span>Sharp</span>
                  <span>Blurred</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Add a subtle blur effect to create that authentic brat aesthetic
                </p>
              </div>

              {/* Smart Font Size Toggle */}
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <label className="block text-lg font-semibold text-black">
                    Smart Font Size
                  </label>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={useAutoFontSize}
                      onChange={(e) => setUseAutoFontSize(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Automatically adjusts font size based on text length for optimal balance
                </p>
                {useAutoFontSize && text.trim() && (
                  <p className="text-sm text-green-600 mt-1 font-medium">
                    Current font size: {getEffectiveFontSize()}px
                  </p>
                )}
              </div>

              {/* Manual Font Size (only when smart sizing is off) */}
              {!useAutoFontSize && (
                <div className="mb-8">
                  <label className="block text-lg font-semibold text-black mb-2">
                    Font Size: {fontSize}px
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="120"
                    value={fontSize}
                    onChange={(e) => setFontSize(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              )}

              {/* Generate Button */}
              <button
                onClick={generateBrat}
                disabled={isGenerating || !text.trim()}
                className="w-full bg-gradient-to-r from-green-500 to-lime-500 text-white py-4 px-6 rounded-xl font-semibold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:transform-none flex items-center justify-center"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Download className="w-5 h-5 mr-2" />
                    Download
                  </>
                )}
              </button>
            </div>
          </ScrollReveal>

          {/* Preview */}
          <ScrollReveal delay={400}>
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-green-100 shadow-xl">
              <h2 className="text-2xl font-bold mb-6 flex items-center text-black">
                <Palette className="w-6 h-6 mr-2 text-green-600" />
                Preview Your Cover
              </h2>

              <div className="aspect-square bg-gray-100 rounded-2xl flex items-center justify-center mb-6 overflow-hidden">
                {previewImageUrl ? (
                  <img
                    src={previewImageUrl}
                    alt="Brat Cover Preview"
                    className="w-full h-full object-cover rounded-2xl"
                    style={{
                      filter: blurIntensity > 0 ? `blur(${blurIntensity}px)` : 'none'
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Type className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Preview will appear here</p>
                    </div>
                  </div>
                )}
              </div>

            </div>
          </ScrollReveal>
        </div>

        {/* How It Works */}
        <ScrollReveal delay={600}>
          <div className="mt-16 text-center">
            <h2 className="text-3xl font-bold mb-8 text-black">How Brat Generator Works?</h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Type className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-black">Enter Your Text</h3>
                <p className="text-gray-700">Type the text you want to appear on your brat cover</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-lime-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Palette className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-black">Choose Colors</h3>
                <p className="text-gray-700">Select your preferred background and text colors</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Download className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-black">Generate & Download</h3>
                <p className="text-gray-700">Preview your cover and download it as a PNG image</p>
              </div>
            </div>
          </div>
        </ScrollReveal>

        {/* Key Features */}
        <ScrollReveal delay={800}>
          <div className="mt-16">
            <h2 className="text-3xl font-bold text-center mb-12 text-black">Key Features</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Type className="w-8 h-8 text-green-600" />,
                  title: "Customization",
                  description: "Make your text stand out. Choose between a brat cover or clean white background and add your own text and style."
                },
                {
                  icon: <Sparkles className="w-8 h-8 text-lime-600" />,
                  title: "Instant Drafts",
                  description: "Brat Generator creates user input and produces the first draft covers ten seconds for quicker creative possibilities."
                },
                {
                  icon: <Coffee className="w-8 h-8 text-yellow-600" />,
                  title: "Ease of Use",
                  description: "No bloated user interface, delivering the straightest, smoothest Brat Generator without sacrificing functionality."
                },
                {
                  icon: <CheckCircle className="w-8 h-8 text-green-600" />,
                  title: "Quality Output",
                  description: "Every image you create delivers clear, vibrant output. Your creative output will fulfill expectations of high quality at optimized output and no pixelated blur."
                },
                {
                  icon: <Clock className="w-8 h-8 text-lime-600" />,
                  title: "Time Selective",
                  description: "You want it customized or a customized preset that includes numerous font styles with perfect spacing so you're never guessing how to construct."
                },
                {
                  icon: <Heart className="w-8 h-8 text-red-500" />,
                  title: "Built-in Templates",
                  description: "As standard starting point for brands, content creators and professionals Brat Generator delivers custom styled and branded components for all categories."
                }
              ].map((feature, index) => (
                <div key={index} className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-green-100">
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-2 text-black">{feature.title}</h3>
                  <p className="text-gray-700">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </ScrollReveal>

        {/* Who Benefits Most */}
        <ScrollReveal delay={1000}>
          <div className="mt-16">
            <h2 className="text-3xl font-bold text-center mb-12 text-black">Who Benefits Most</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: <Users className="w-12 h-12 text-green-600" />,
                  title: "Creators",
                  description: "Research who does or framework projects or map to text or get better content."
                },
                {
                  icon: <Star className="w-12 h-12 text-lime-600" />,
                  title: "Designers",
                  description: "Design thoughtful design items, graphics, or project plans, easily and quickly."
                },
                {
                  icon: <Shield className="w-12 h-12 text-yellow-600" />,
                  title: "Marketers",
                  description: "SEO-optioned assets with effective keyword density for all generated resources."
                }
              ].map((benefit, index) => (
                <div key={index} className="bg-gradient-to-br from-green-500/10 to-lime-500/10 rounded-2xl p-8 text-center">
                  <div className="flex justify-center mb-4">{benefit.icon}</div>
                  <h3 className="text-xl font-semibold mb-4 text-black">{benefit.title}</h3>
                  <p className="text-gray-700">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </ScrollReveal>

        {/* FAQ Section */}
        <ScrollReveal delay={1000}>
          <div className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 text-black flex items-center justify-center">
                <HelpCircle className="w-8 h-8 mr-3 text-green-600" />
                Frequently Asked Questions
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Got questions? We've got answers! Find solutions to common questions about using Brat Generator.
              </p>
            </div>

            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-green-100 shadow-xl">
              <div className="space-y-4">
                {faqData.map((faq, index) => (
                  <div key={index} className="border border-gray-200 rounded-xl overflow-hidden">
                    <button
                      onClick={() => setOpenFaq(openFaq === index ? null : index)}
                      className="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
                    >
                      <span className="font-semibold text-gray-900">{faq.question}</span>
                      {openFaq === index ? (
                        <ChevronUp className="w-5 h-5 text-green-600" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-green-600" />
                      )}
                    </button>
                    {openFaq === index && (
                      <div className="px-6 py-4 bg-white">
                        <p className="text-gray-700">{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-8 text-center p-6 bg-green-50 rounded-xl border border-green-200">
                <h3 className="font-semibold text-green-900 mb-2">Still have questions?</h3>
                <p className="text-green-700 mb-4">
                  Can't find what you're looking for? We're here to help!
                </p>
                <a 
                  href="/contact"
                  className="inline-flex items-center space-x-2 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors"
                >
                  <span>Contact Us</span>
                  <ArrowRight className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        </ScrollReveal>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16 mt-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            {/* Logo and Description */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 rounded-lg flex items-center justify-center">
                  <img 
                    src="/favicon.ico" 
                    alt="Brat Generator Logo" 
                    width={32} 
                    height={32}
                    className="rounded-lg"
                  />
                </div>
                <span className="text-xl font-bold">Brat Generator</span>
              </div>
              <p className="text-gray-400 max-w-md">
                Create stunning brat-style covers instantly. Free, fast, and fun - perfect for social media, album covers, and more.
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/" className="hover:text-white transition-colors">Home</a></li>
                <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="/terms" className="hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2025 Brat Generator. Built with ❤️ | 
              <a href="mailto:<EMAIL>" className="hover:text-white transition-colors ml-1">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}