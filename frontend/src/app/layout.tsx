import type { Metadata, Viewport } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Brat Generator - Create Custom Brat Style Covers Instantly",
  description: "Brat Generator is an innovative online tool that helps you turn your ideas into bold, eye-catching brat-style text covers quickly. Create custom covers with personalized text, colors, and styles instantly.",
  keywords: ["brat generator", "brat cover", "text generator", "album cover generator", "custom text", "brat style", "cover creator", "text to image", "brat album", "charli xcx", "pop culture"],
  authors: [{ name: "Brat Generator" }],
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/favicon.ico",
  },
  openGraph: {
    title: "Brat Generator - Create Custom Brat Style Covers",
    description: "Create your own custom brat-style covers instantly. Just type, choose colors, and generate bold, eye-catching covers.",
    type: "website",
    locale: "en_US",
    siteName: "Brat Generator",
    url: "https://www.brat-generator.me",
    images: [
      {
        url: "https://image.apiihub.com/brat.png",
        width: 1200,
        height: 630,
        alt: "Brat Generator - Create Custom Brat Style Covers",
      },
    ],
  },
  twitter: {
    card: "summary_large_image", 
    title: "Brat Generator - Create Custom Brat Style Covers",
    description: "Create your own custom brat-style covers instantly. Just type, choose colors, and generate!",
    images: ["https://image.apiihub.com/brat.png"],
  },
  robots: "index, follow",
  alternates: {
    canonical: "/",
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" style={{colorScheme: 'light'}}>
      <head>
        <meta name="color-scheme" content="light" />
        <style dangerouslySetInnerHTML={{
          __html: `
            * { color-scheme: light !important; }
            html { color-scheme: light !important; }
            body { color-scheme: light !important; color: #171717 !important; background: #ffffff !important; }
          `
        }} />
      </head>
      <body className="antialiased" style={{colorScheme: 'light', color: '#171717', background: '#ffffff'}}>
        {children}
      </body>
    </html>
  );
}