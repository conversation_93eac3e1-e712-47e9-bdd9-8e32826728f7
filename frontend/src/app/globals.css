@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Force light theme for all pages - no dark mode support */
* {
  color-scheme: light !important;
}

html {
  color-scheme: light !important;
}

body {
  color: #171717 !important;
  background: #ffffff !important;
  font-family: Arial, Helvetica, sans-serif;
  color-scheme: light !important;
}

/* Completely disable dark mode */
@media (prefers-color-scheme: dark) {
  * {
    color-scheme: light !important;
  }
  
  body {
    color: #171717 !important;
    background: #ffffff !important;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Animation classes */
.hover-float:hover {
  animation: float 6s ease-in-out infinite;
}

.magnetic-hover:hover {
  animation: magnetic 0.3s ease-out;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* Blog article styles */
.article-content h1,
.article-content h2,
.article-content h3 {
  scroll-margin-top: 2rem;
  color: #374151 !important; /* Force dark gray text */
}

.article-content p,
.article-content li,
.article-content div {
  color: #4b5563 !important; /* Force dark gray text */
}

.article-content strong {
  color: #1f2937 !important; /* Force darker text for bold */
}

/* Force dark text on blog pages regardless of system theme */
html body .blog-page,
html body .blog-page *,
html body .blog-page *::before,
html body .blog-page *::after {
  color: #1f2937 !important;
  color-scheme: light !important;
}

/* Nuclear option - force all text to be visible */
.blog-page * {
  color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.blog-page {
  background: #ffffff !important;
  color-scheme: light !important;
}

.blog-page .text-gray-600,
.blog-page [class*="text-gray-600"] {
  color: #6b7280 !important;
}

.blog-page .text-gray-700,
.blog-page [class*="text-gray-700"] {
  color: #4b5563 !important;
}

.blog-page .text-gray-800,
.blog-page [class*="text-gray-800"] {
  color: #374151 !important;
}

.blog-page .text-gray-500,
.blog-page [class*="text-gray-500"] {
  color: #9ca3af !important;
}

/* Override any inherited white text */
.blog-page h1,
.blog-page h2,
.blog-page h3,
.blog-page h4,
.blog-page h5,
.blog-page h6 {
  color: #1f2937 !important;
}

.blog-page p,
.blog-page div,
.blog-page span,
.blog-page .prose,
.blog-page .prose *,
.blog-page .article-content,
.blog-page .article-content * {
  color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.blog-page a {
  color: #059669 !important;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}