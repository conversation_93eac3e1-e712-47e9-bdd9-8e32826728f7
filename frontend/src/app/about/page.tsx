import { <PERSON>ada<PERSON> } from 'next';
import <PERSON> from 'next/link';
import { ArrowRight, Sparkles, Users, Heart, Target, Globe, Zap } from 'lucide-react';
import Navbar from '@/components/Navbar';
import ScrollReveal from '@/components/ScrollReveal';

export const metadata: Metadata = {
  title: 'About | Brat Generator - Create Stunning Brat-Style Covers',
  description: 'Learn about Brat Generator, the free online tool for creating stunning brat-style covers. Our mission, story, and commitment to empowering creativity.',
  keywords: ['about brat generator', 'brat covers', 'album cover design', 'creative tools', 'design platform'],
  openGraph: {
    title: 'About Brat Generator',
    description: 'Learn about our mission to democratize creative design and empower artists worldwide.',
    type: 'website',
    siteName: 'Brat Generator',
    url: 'https://www.brat-generator.me/about',
    images: [
      {
        url: 'https://image.apiihub.com/brat.png',
        width: 1200,
        height: 630,
        alt: 'About Brat Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Brat Generator',
    description: 'Learn about our mission to democratize creative design and empower artists worldwide.',
    images: ['https://image.apiihub.com/brat.png'],
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50">
      <Navbar />

      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Hero Section */}
        <ScrollReveal>
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-green-200 mb-6">
              <Sparkles className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-sm font-medium text-green-700">About Us</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent">
                Democratizing Creative Design
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Brat Generator is more than just a design tool—it's a creative movement that empowers artists, musicians, and creators to express themselves through bold, impactful visual design.
            </p>
          </div>
        </ScrollReveal>

        {/* Mission Section */}
        <ScrollReveal delay={200}>
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-green-100 shadow-xl mb-16">
            <div className="flex items-center mb-6">
              <Target className="w-8 h-8 text-green-600 mr-3" />
              <h2 className="text-3xl font-bold text-gray-800">Our Mission</h2>
            </div>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              We believe that every artist deserves access to professional-quality design tools, regardless of their budget or technical expertise. Our mission is to democratize creative design by providing free, intuitive tools that help creators bring their vision to life.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed">
              The brat aesthetic represents more than just a visual style—it's about confidence, authenticity, and breaking down barriers. We're committed to supporting this movement by making high-quality design accessible to everyone.
            </p>
          </div>
        </ScrollReveal>

        {/* Story Section */}
        <ScrollReveal delay={400}>
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 flex items-center">
              <Heart className="w-8 h-8 text-red-500 mr-3" />
              Our Story
            </h2>
            <div className="bg-gradient-to-r from-green-500 to-lime-500 rounded-3xl p-8 text-white">
              <p className="text-lg leading-relaxed mb-6">
                Brat Generator was born from a simple observation: amazing music was being held back by expensive design barriers. Too many talented artists were struggling to create professional-looking covers that matched the quality of their music.
              </p>
              <p className="text-lg leading-relaxed mb-6">
                Inspired by the bold, unapologetic brat aesthetic that was taking the music world by storm, we set out to create a tool that would let anyone—regardless of design experience—create stunning, professional-quality covers in minutes.
              </p>
              <p className="text-lg leading-relaxed">
                What started as a simple cover generator has evolved into a comprehensive platform that serves thousands of creators worldwide, helping them express their artistic vision through powerful visual design.
              </p>
            </div>
          </div>
        </ScrollReveal>

        {/* Values Section */}
        <ScrollReveal delay={600}>
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">What We Stand For</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: <Users className="w-12 h-12 text-blue-600" />,
                  title: "Accessibility",
                  description: "Creative tools should be free and available to everyone, regardless of budget or location."
                },
                {
                  icon: <Globe className="w-12 h-12 text-green-600" />,
                  title: "Inclusivity",
                  description: "We celebrate diverse voices and support creators from all backgrounds and communities."
                },
                {
                  icon: <Zap className="w-12 h-12 text-yellow-600" />,
                  title: "Innovation",
                  description: "We continuously evolve our tools to meet the changing needs of modern creators."
                }
              ].map((value, index) => (
                <div key={index} className="bg-white/70 backdrop-blur-sm rounded-2xl p-8 border border-green-100 text-center hover:shadow-lg transition-all duration-300">
                  <div className="flex justify-center mb-4">{value.icon}</div>
                  <h3 className="text-xl font-semibold mb-4 text-gray-800">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </ScrollReveal>

        {/* Impact Section */}
        <ScrollReveal delay={800}>
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-green-100 shadow-xl mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Our Impact</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
              {[
                { number: "50,000+", label: "Covers Created" },
                { number: "25+", label: "Countries Served" },
                { number: "15,000+", label: "Active Users" },
                { number: "100%", label: "Free Forever" }
              ].map((stat, index) => (
                <div key={index}>
                  <div className="text-3xl font-bold text-green-600 mb-2">{stat.number}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </ScrollReveal>

        {/* Future Section */}
        <ScrollReveal delay={1000}>
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Looking Forward</h2>
            <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl p-8 text-white text-center">
              <p className="text-lg leading-relaxed mb-6">
                We're just getting started. Our roadmap includes advanced design features, collaborative tools, and integration with major music platforms—all while maintaining our commitment to keeping our core tools free and accessible.
              </p>
              <p className="text-lg leading-relaxed">
                The future of creative design is collaborative, inclusive, and empowering. We're excited to be part of this journey with our amazing community of creators.
              </p>
            </div>
          </div>
        </ScrollReveal>

        {/* Call to Action */}
        <ScrollReveal delay={1200}>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">Ready to Create?</h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of creators who are already using Brat Generator to bring their artistic vision to life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/"
                className="bg-gradient-to-r from-green-500 to-lime-500 text-white px-8 py-4 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200 inline-flex items-center justify-center"
              >
                Start Creating
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link 
                href="/blog"
                className="border-2 border-green-500 text-green-600 px-8 py-4 rounded-full font-semibold hover:bg-green-50 transition-all duration-200 inline-flex items-center justify-center"
              >
                Learn More
              </Link>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </div>
  );
}