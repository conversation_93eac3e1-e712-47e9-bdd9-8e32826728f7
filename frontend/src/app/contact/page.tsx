import Navbar from '@/components/Navbar';
import { Mail, MessageSquare, Clock, CheckCircle } from 'lucide-react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - Brat Generator',
  description: 'Get in touch with the Brat Generator team. We\'re here to help with any questions or feedback about our brat cover generator.',
  openGraph: {
    title: 'Contact Us - Brat Generator',
    description: 'Get in touch with the Brat Generator team. We\'re here to help with any questions or feedback about our brat cover generator.',
    type: 'website',
    siteName: 'Brat Generator',
    url: 'https://www.brat-generator.me/contact',
    images: [
      {
        url: 'https://image.apiihub.com/brat.png',
        width: 1200,
        height: 630,
        alt: 'Contact Us - Brat Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - Brat Generator',
    description: 'Get in touch with the Brat Generator team. We\'re here to help with any questions or feedback about our brat cover generator.',
    images: ['https://image.apiihub.com/brat.png'],
  },
  robots: 'index, follow',
  alternates: {
    canonical: '/contact',
  },
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50">
      <Navbar />
      
      <div className="max-w-6xl mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent">
            Contact Us
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Have questions, feedback, or need help? We'd love to hear from you!
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
              <h2 className="text-2xl font-bold mb-6 text-gray-900">Get in Touch</h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Email Support</h3>
                    <p className="text-gray-600 mb-2">Send us an email and we'll get back to you as soon as possible.</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-green-600 font-medium hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-lime-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Response Time</h3>
                    <p className="text-gray-600">We typically respond within 24 hours during business days.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Feedback Welcome</h3>
                    <p className="text-gray-600">We love hearing your ideas for improving Brat Generator!</p>
                  </div>
                </div>
              </div>
            </div>

            {/* FAQ Preview */}
            <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
              <h2 className="text-2xl font-bold mb-6 text-gray-900">Quick Help</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Common Questions:</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• How to download high-quality images</li>
                    <li>• Font and color customization</li>
                    <li>• Technical issues and troubleshooting</li>
                    <li>• Feature requests and suggestions</li>
                  </ul>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-500">
                    For detailed answers, check out our FAQ section on the homepage.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form Area */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
            <h2 className="text-2xl font-bold mb-6 text-gray-900">Send us a Message</h2>
            
            <div className="space-y-6">
              <p className="text-gray-600">
                While we don't have a contact form yet, you can reach us directly via email for:
              </p>
              
              <div className="grid sm:grid-cols-2 gap-4">
                <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-semibold text-green-900">Bug Reports</h3>
                  </div>
                  <p className="text-sm text-green-700">Found an issue? Let us know!</p>
                </div>
                
                <div className="bg-lime-50 rounded-xl p-4 border border-lime-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-lime-600" />
                    <h3 className="font-semibold text-lime-900">Feature Ideas</h3>
                  </div>
                  <p className="text-sm text-lime-700">Suggest new features</p>
                </div>
                
                <div className="bg-yellow-50 rounded-xl p-4 border border-yellow-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-yellow-600" />
                    <h3 className="font-semibold text-yellow-900">General Help</h3>
                  </div>
                  <p className="text-sm text-yellow-700">Need assistance using the tool?</p>
                </div>
                
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-blue-600" />
                    <h3 className="font-semibold text-blue-900">Partnerships</h3>
                  </div>
                  <p className="text-sm text-blue-700">Business inquiries welcome</p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-3">When contacting us, please include:</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Your browser and operating system</li>
                  <li>• Steps to reproduce any issues</li>
                  <li>• Screenshots if helpful</li>
                  <li>• Any error messages you see</li>
                </ul>
              </div>

              <div className="text-center">
                <a 
                  href="mailto:<EMAIL>?subject=Brat Generator - Support Request"
                  className="inline-flex items-center space-x-2 bg-gradient-to-r from-green-500 to-lime-500 text-white py-3 px-6 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200"
                >
                  <Mail className="w-5 h-5" />
                  <span>Email Us Now</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}