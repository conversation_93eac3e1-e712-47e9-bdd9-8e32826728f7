import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Authentication - Brat Generator',
  description: 'Sign in to Brat Generator to access premium features and save your custom brat covers.',
  openGraph: {
    title: 'Authentication - Brat Generator',
    description: 'Sign in to Brat Generator to access premium features and save your custom brat covers.',
    type: 'website',
    siteName: 'Brat Generator',
    url: 'https://www.brat-generator.me/auth',
    images: [
      {
        url: 'https://image.apiihub.com/brat.png',
        width: 1200,
        height: 630,
        alt: 'Authentication - Brat Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Authentication - Brat Generator',
    description: 'Sign in to Brat Generator to access premium features and save your custom brat covers.',
    images: ['https://image.apiihub.com/brat.png'],
  },
  robots: 'noindex, nofollow',
  alternates: {
    canonical: '/auth',
  },
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}