import Navbar from '@/components/Navbar';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy - Brat Generator',
  description: 'Privacy Policy for Brat Generator - Learn how we protect your data and respect your privacy when using our brat cover generator.',
  openGraph: {
    title: 'Privacy Policy - Brat Generator',
    description: 'Privacy Policy for Brat Generator - Learn how we protect your data and respect your privacy when using our brat cover generator.',
    type: 'website',
    siteName: 'Brat Generator',
    url: 'https://www.brat-generator.me/privacy',
    images: [
      {
        url: 'https://image.apiihub.com/brat.png',
        width: 1200,
        height: 630,
        alt: 'Privacy Policy - Brat Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Privacy Policy - Brat Generator',
    description: 'Privacy Policy for Brat Generator - Learn how we protect your data and respect your privacy when using our brat cover generator.',
    images: ['https://image.apiihub.com/brat.png'],
  },
  robots: 'index, follow',
  alternates: {
    canonical: '/privacy',
  },
};

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
          <h1 className="text-4xl font-bold mb-8 bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent">
            Privacy Policy
          </h1>
          
          <div className="space-y-8 text-gray-700">
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Information We Collect</h2>
              <p className="mb-4">
                Brat Generator is committed to protecting your privacy. We collect minimal information necessary to provide our service:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Text input you provide to generate brat covers</li>
                <li>Color and font preferences you select</li>
                <li>Basic usage analytics to improve our service</li>
                <li>Technical information like IP address and browser type for security purposes</li>
              </ul>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">How We Use Your Information</h2>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>To generate and provide your custom brat covers</li>
                <li>To improve our service and user experience</li>
                <li>To ensure the security and proper functioning of our website</li>
                <li>To respond to your inquiries and provide customer support</li>
              </ul>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Data Storage and Processing</h2>
              <p className="mb-4">
                Your text inputs and generated images are processed temporarily to create your brat covers. We do not permanently store your personal content on our servers. Generated images are created on-demand and delivered directly to your browser.
              </p>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Cookies and Tracking</h2>
              <p className="mb-4">
                We use minimal cookies and tracking technologies to:
              </p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Remember your preferences (colors, fonts)</li>
                <li>Analyze website usage to improve our service</li>
                <li>Ensure website security and prevent abuse</li>
              </ul>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Third-Party Services</h2>
              <p className="mb-4">
                We may use third-party services for analytics and website functionality. These services have their own privacy policies and data handling practices.
              </p>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Your Rights</h2>
              <p className="mb-4">You have the right to:</p>
              <ul className="list-disc list-inside space-y-2 ml-4">
                <li>Access information about how your data is processed</li>
                <li>Request deletion of any personal data we may have</li>
                <li>Opt out of analytics tracking</li>
                <li>Contact us with privacy-related questions</li>
              </ul>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Contact Us</h2>
              <p className="mb-4">
                If you have any questions about this Privacy Policy or our data practices, please contact us at:
              </p>
              <p className="font-medium text-green-600">
                <a href="mailto:<EMAIL>" className="hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>

            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-900">Updates to This Policy</h2>
              <p className="mb-4">
                We may update this Privacy Policy from time to time. Any changes will be posted on this page with an updated revision date.
              </p>
              <p className="text-sm text-gray-500">
                Last updated: {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}