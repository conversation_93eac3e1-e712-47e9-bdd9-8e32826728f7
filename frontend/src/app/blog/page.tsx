'use client';

import Link from 'next/link';
import { BookO<PERSON>, TrendingUp, <PERSON>, Clock, ArrowRight, Star, Calendar, Tag } from 'lucide-react';
import { useState } from 'react';
import Navbar from '@/components/Navbar';
import ScrollReveal from '@/components/ScrollReveal';
import { getAllArticles } from '@/services/blogService';

export default function BlogPage() {
  const [activeCategory, setActiveCategory] = useState('All');
  const articles = getAllArticles();

  const categories = [
    { name: 'All', count: 6 },
    { name: 'Tutorial', count: 1 },
    { name: 'Design', count: 2 },
    { name: 'Marketing', count: 1 },
    { name: 'Branding', count: 1 },
    { name: 'Inspiration', count: 1 }
  ];

  // Filter articles based on active category
  const filteredArticles = activeCategory === 'All' 
    ? articles 
    : articles.filter(article => article.category === activeCategory);

  // Get featured article (always show regardless of filter)
  const featuredArticle = articles.find(article => article.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50 blog-page" style={{colorScheme: 'light', color: '#1f2937'}}>
      <style dangerouslySetInnerHTML={{
        __html: `
          .blog-page, .blog-page * { color: #1f2937 !important; color-scheme: light !important; }
          .blog-page .text-gray-600 { color: #6b7280 !important; }
          .blog-page .text-gray-700 { color: #4b5563 !important; }
          .blog-page .text-gray-800 { color: #374151 !important; }
          .blog-page .text-gray-500 { color: #9ca3af !important; }
        `
      }} />
      <Navbar />

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Page Header */}
        <ScrollReveal>
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-white/60 backdrop-blur-sm rounded-full border border-green-200 mb-6">
              <BookOpen className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-sm font-medium text-green-700">Latest Articles</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent">
                Brat Generator Blog
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover the latest trends, tutorials, and insights about brat generator, image creation, and design. Learn tips and tricks for creating amazing covers.
            </p>
          </div>
        </ScrollReveal>

        {/* Stats */}
        <ScrollReveal delay={200}>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            {[
              { number: '50+', label: 'Articles', icon: BookOpen },
              { number: '10K+', label: 'Readers', icon: Users },
              { number: '25K+', label: 'Views', icon: TrendingUp },
              { number: '4.8/5', label: 'Rating', icon: Star }
            ].map((stat, index) => (
              <div key={index} className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-6 text-center hover:shadow-lg transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-lime-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-800 mb-1">{stat.number}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </ScrollReveal>

        {/* Categories */}
        <ScrollReveal delay={400}>
          <div className="flex flex-wrap gap-4 mb-8">
            {categories.map((category) => (
              <button
                key={category.name}
                onClick={() => setActiveCategory(category.name)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                  activeCategory === category.name
                    ? 'bg-gradient-to-r from-green-500 to-lime-500 text-white shadow-lg'
                    : 'bg-white/70 text-gray-700 border border-green-200 hover:border-green-300 hover:shadow-md'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </ScrollReveal>

        {/* Featured Article - Always show */}
        {featuredArticle && (
          <ScrollReveal delay={600}>
            <div className="bg-gradient-to-r from-green-500 to-lime-500 rounded-3xl p-8 mb-12 text-white">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-sm font-medium mb-4">
                    <Star className="w-4 h-4 mr-2" />
                    Featured Article
                  </div>
                  <h2 className="text-3xl font-bold mb-4">{featuredArticle.title}</h2>
                  <p className="text-white mb-6 leading-relaxed">{featuredArticle.description}</p>
                  <div className="flex items-center gap-6 mb-6">
                    <div className="flex items-center gap-2 text-white">
                      <Clock className="w-4 h-4" />
                      <span className="text-sm">{featuredArticle.readTime}</span>
                    </div>
                    <div className="flex items-center gap-2 text-white">
                      <Calendar className="w-4 h-4" />
                      <span className="text-sm">{featuredArticle.date}</span>
                    </div>
                    <div className="px-3 py-1 bg-white/20 rounded-full text-sm">
                      {featuredArticle.category}
                    </div>
                  </div>
                  <Link 
                    href={`/blog/${featuredArticle.id}`}
                    className="inline-flex items-center gap-2 bg-white text-green-600 px-6 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200"
                  >
                    Read Article
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
                <div className="text-center">
                  <div className="text-8xl mb-4">{featuredArticle.image}</div>
                  <div className="text-white text-sm">Featured Content</div>
                </div>
              </div>
            </div>
          </ScrollReveal>
        )}

        {/* Articles Grid - Filtered */}
        <ScrollReveal delay={800}>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.filter(article => !article.featured).map((article) => (
              <article key={article.id} className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl overflow-hidden hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                <div className="p-6">
                  <div className="text-4xl mb-4 text-center">{article.image}</div>
                  <div className="flex items-center gap-4 mb-4">
                    <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-lime-100 text-green-700 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                    <div className="flex items-center gap-1 text-gray-500 text-sm">
                      <Clock className="w-4 h-4" />
                      {article.readTime}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-3">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">{article.description}</p>
                  
                  {/* Article Meta */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      {article.date}
                    </div>
                    <span>by {article.author}</span>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {article.tags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center gap-1 px-2 py-1 bg-green-50 text-green-600 rounded-lg text-xs">
                        <Tag className="w-3 h-3" />
                        {tag}
                      </span>
                    ))}
                  </div>

                  <Link 
                    href={`/blog/${article.id}`}
                    className="inline-flex items-center gap-2 text-green-600 font-semibold hover:text-green-800 transition-colors"
                  >
                    Read More
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </ScrollReveal>

        {/* No Results Message */}
        {filteredArticles.filter(article => !article.featured).length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-xl font-semibold mb-2">No articles found</h3>
              <p>Try selecting a different category to see more articles.</p>
            </div>
          </div>
        )}

        {/* Newsletter Subscription */}
        <ScrollReveal delay={1000}>
          <div className="mt-16 bg-gradient-to-r from-green-500 to-lime-500 rounded-3xl p-8 text-white text-center">
            <h3 className="text-3xl font-bold mb-4">Stay Creative</h3>
            <p className="text-lg mb-6 max-w-2xl mx-auto">
              Subscribe to get the latest design tips, brat generator updates, and creative inspiration delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-2 focus:ring-white"
              />
              <button className="bg-white text-green-600 px-6 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-200">
                Subscribe
              </button>
            </div>
          </div>
        </ScrollReveal>
      </div>
    </div>
  );
}