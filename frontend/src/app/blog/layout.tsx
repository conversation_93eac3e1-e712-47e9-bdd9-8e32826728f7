import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Blog | Brat Generator - Design Tips, Tutorials & Inspiration',
  description: 'Discover the latest trends, tutorials, and insights about brat generator, image creation, and design. Learn tips and tricks for creating amazing covers.',
  keywords: ['brat generator blog', 'design tutorials', 'album cover design', 'typography trends', 'creative inspiration'],
  openGraph: {
    title: 'Brat Generator Blog',
    description: 'Discover the latest trends, tutorials, and insights about brat generator, image creation, and design.',
    type: 'website',
    siteName: 'Brat Generator',
    url: 'https://www.brat-generator.me/blog',
    images: [
      {
        url: 'https://image.apiihub.com/brat.png',
        width: 1200,
        height: 630,
        alt: 'Brat Generator Blog',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Brat Generator Blog',
    description: 'Discover the latest trends, tutorials, and insights about brat generator, image creation, and design.',
    images: ['https://image.apiihub.com/brat.png'],
  },
  robots: 'index, follow',
  alternates: {
    canonical: '/blog',
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}