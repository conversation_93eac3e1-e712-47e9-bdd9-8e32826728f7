import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, ArrowLeft, Home, Search } from 'lucide-react';
import Navbar from '@/components/Navbar';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* 404 Visual */}
          <div className="mb-8">
            <div className="text-8xl mb-4">📝</div>
            <h1 className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent mb-4">
              404
            </h1>
          </div>
          
          {/* Error Message */}
          <div className="mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
              Article Not Found
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Sorry, we couldn't find the article you're looking for. It might have been moved, deleted, or the URL might be incorrect.
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Link 
              href="/blog"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-lime-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200"
            >
              <BookOpen className="w-4 h-4" />
              Browse All Articles
            </Link>
            
            <Link 
              href="/"
              className="inline-flex items-center gap-2 border-2 border-green-500 text-green-600 px-8 py-3 rounded-full font-semibold hover:bg-green-50 transition-all duration-200"
            >
              <Home className="w-4 h-4" />
              Back to Home
            </Link>
          </div>
          
          {/* Search Suggestion */}
          <div className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-8 max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Search className="w-6 h-6 text-green-600" />
              <h3 className="text-xl font-semibold text-gray-800">
                Looking for something specific?
              </h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Try browsing our categories or check out our popular articles below.
            </p>
            
            {/* Quick Links */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {[
                { name: 'Tutorial', href: '/blog?category=Tutorial' },
                { name: 'Design', href: '/blog?category=Design' },
                { name: 'Marketing', href: '/blog?category=Marketing' },
                { name: 'Branding', href: '/blog?category=Branding' },
                { name: 'Inspiration', href: '/blog?category=Inspiration' },
                { name: 'All Articles', href: '/blog' }
              ].map((category) => (
                <Link
                  key={category.name}
                  href={category.href}
                  className="px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm font-medium hover:bg-green-100 transition-colors text-center"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}