import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { 
  Clock, 
  Calendar, 
  User, 
  Tag, 
  ArrowLeft, 
  Share2, 
  Facebook, 
  Twitter, 
  Linkedin,
  ChevronLeft,
  ChevronRight,
  BookOpen
} from 'lucide-react';
import { getArticleBySlug, getAllArticles, getRelatedArticles } from '@/services/blogService';
import Navbar from '@/components/Navbar';
import ScrollReveal from '@/components/ScrollReveal';

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const article = getArticleBySlug(slug);

  if (!article) {
    return {
      title: 'Article Not Found | Brat Generator Blog',
      description: 'The requested article could not be found.',
    };
  }

  return {
    title: article.seo.metaTitle,
    description: article.seo.metaDescription,
    keywords: article.seo.keywords,
    authors: [{ name: article.author }],
    openGraph: {
      title: article.seo.metaTitle,
      description: article.seo.metaDescription,
      type: 'article',
      publishedTime: article.date,
      authors: [article.author],
      tags: article.tags,
      siteName: 'Brat Generator',
      url: `https://www.brat-generator.me/blog/${slug}`,
      images: [
        {
          url: 'https://image.apiihub.com/brat.png',
          width: 1200,
          height: 630,
          alt: article.seo.metaTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: article.seo.metaTitle,
      description: article.seo.metaDescription,
      images: ['https://image.apiihub.com/brat.png'],
    },
    alternates: {
      canonical: `/blog/${slug}`,
    },
  };
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  const articles = getAllArticles();
  return articles.map((article) => ({
    slug: article.id,
  }));
}

// JSON-LD structured data for SEO
function generateStructuredData(article: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.description,
    author: {
      '@type': 'Person',
      name: article.author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Brat Generator',
      logo: {
        '@type': 'ImageObject',
        url: 'https://bratgenerator.com/logo.png',
      },
    },
    datePublished: article.date,
    dateModified: article.date,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://brat-generator.me/blog/${article.id}`,
    },
    articleSection: article.category,
    keywords: article.tags.join(', '),
    wordCount: article.content.split(' ').length,
    timeRequired: article.readTime,
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const article = getArticleBySlug(slug);
  
  if (!article) {
    notFound();
  }

  const relatedArticles = getRelatedArticles(article.id);
  const allArticles = getAllArticles();
  const currentIndex = allArticles.findIndex(a => a.id === article.id);
  const previousArticle = currentIndex > 0 ? allArticles[currentIndex - 1] : null;
  const nextArticle = currentIndex < allArticles.length - 1 ? allArticles[currentIndex + 1] : null;

  const shareUrl = `https://brat-generator.me/blog/${article.id}`;
  const shareText = `Check out this article: ${article.title}`;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-lime-50 to-yellow-50 blog-page" style={{colorScheme: 'light', color: '#1f2937'}}>
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Critical: Override all global styles with maximum specificity */
          html[lang="en"] body .blog-page,
          html[lang="en"] body .blog-page *,
          html[lang="en"] body .blog-page *::before,
          html[lang="en"] body .blog-page *::after {
            color: #1f2937 !important;
            color-scheme: light !important;
          }
          
          /* Override any CSS variables or custom properties */
          .blog-page {
            --text-color: #1f2937 !important;
            --bg-color: #ffffff !important;
          }
          
          /* Force override with highest specificity */
          body .blog-page h1,
          body .blog-page h2, 
          body .blog-page h3,
          body .blog-page h4,
          body .blog-page h5,
          body .blog-page h6 { 
            color: #1f2937 !important; 
          }
          
          body .blog-page p,
          body .blog-page div,
          body .blog-page span,
          body .blog-page td,
          body .blog-page th,
          body .blog-page li,
          body .blog-page dt,
          body .blog-page dd { 
            color: #4b5563 !important; 
          }
          
          body .blog-page a { 
            color: #059669 !important; 
          }
          
          /* Article content with maximum specificity */
          body .blog-page .article-content,
          body .blog-page .article-content * {
            color: #4b5563 !important;
          }
          
          body .blog-page .article-content h1,
          body .blog-page .article-content h2,
          body .blog-page .article-content h3 {
            color: #1f2937 !important;
          }
          
          /* Override any inherited or computed styles */
          .blog-page [style*="color"] {
            color: #1f2937 !important;
          }
          
          /* Override specific Tailwind classes */
          .blog-page .text-gray-600 { color: #6b7280 !important; }
          .blog-page .text-gray-700 { color: #4b5563 !important; }
          .blog-page .text-gray-800 { color: #1f2937 !important; }
          .blog-page .text-gray-500 { color: #9ca3af !important; }
          
          /* Last resort: nuclear option */
          .blog-page * {
            color: #1f2937 !important;
          }
        `
      }} />
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateStructuredData(article)),
        }}
      />
      
      <Navbar />

      <article className="max-w-4xl mx-auto px-4 py-8">
        {/* Back to Blog Link */}
        <ScrollReveal>
          <Link 
            href="/blog"
            className="inline-flex items-center gap-2 text-green-600 hover:text-green-800 transition-colors mb-8 group"
          >
            <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
            Back to Blog
          </Link>
        </ScrollReveal>

        {/* Article Header */}
        <ScrollReveal delay={200}>
          <header className="mb-12">
            <div className="flex items-center gap-4 mb-6">
              <span className="px-4 py-2 bg-gradient-to-r from-green-100 to-lime-100 text-green-700 rounded-full text-sm font-medium">
                {article.category}
              </span>
              {article.featured && (
                <span className="px-4 py-2 bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 rounded-full text-sm font-medium">
                  Featured
                </span>
              )}
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-green-600 to-lime-600 bg-clip-text text-transparent leading-tight">
              {article.title}
            </h1>

            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {article.description}
            </p>

            {/* Article Meta */}
            <div className="flex flex-wrap items-center gap-6 text-gray-500 mb-8">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span className="text-sm">by {article.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span className="text-sm">{new Date(article.date).toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span className="text-sm">{article.readTime}</span>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-8">
              {article.tags.map((tag, index) => (
                <span 
                  key={index} 
                  className="inline-flex items-center gap-1 px-3 py-1 bg-white/70 text-green-600 rounded-lg text-sm border border-green-200"
                >
                  <Tag className="w-3 h-3" />
                  {tag}
                </span>
              ))}
            </div>

            {/* Social Share */}
            <div className="flex items-center gap-4 p-4 bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl">
              <span className="text-sm font-medium text-gray-700">Share this article:</span>
              <div className="flex items-center gap-2">
                <a
                  href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                  aria-label="Share on Twitter"
                >
                  <Twitter className="w-4 h-4" />
                </a>
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  aria-label="Share on Facebook"
                >
                  <Facebook className="w-4 h-4" />
                </a>
                <a
                  href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-500 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
                  aria-label="Share on LinkedIn"
                >
                  <Linkedin className="w-4 h-4" />
                </a>
                <div className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors cursor-pointer" title="Copy link functionality available in browser">
                  <Share2 className="w-4 h-4" />
                </div>
              </div>
            </div>
          </header>
        </ScrollReveal>

        {/* Featured Image */}
        {article.featuredImage && (
          <ScrollReveal delay={300}>
            <div className="mb-12">
              <div className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-4 overflow-hidden">
                <img
                  src={article.featuredImage}
                  alt={article.title}
                  className="w-full h-64 md:h-80 lg:h-96 object-cover rounded-xl"
                />
              </div>
            </div>
          </ScrollReveal>
        )}

        {/* Article Content */}
        <ScrollReveal delay={400}>
          <div className="prose prose-lg prose-gray max-w-none mb-16">
            <div className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-8 md:p-12">
              <div 
                className="article-content"
                dangerouslySetInnerHTML={{ 
                  __html: article.content
                    .split('\n')
                    .map(paragraph => {
                      // Handle headers
                      if (paragraph.startsWith('# ')) {
                        return `<h1 class="text-3xl md:text-4xl font-bold mt-12 mb-6 text-gray-800 first:mt-0">${paragraph.substring(2)}</h1>`;
                      }
                      if (paragraph.startsWith('## ')) {
                        return `<h2 class="text-2xl md:text-3xl font-bold mt-10 mb-5 text-gray-800">${paragraph.substring(3)}</h2>`;
                      }
                      if (paragraph.startsWith('### ')) {
                        return `<h3 class="text-xl md:text-2xl font-bold mt-8 mb-4 text-gray-800">${paragraph.substring(4)}</h3>`;
                      }
                      
                      // Handle bold text
                      if (paragraph.startsWith('**') && paragraph.endsWith('**')) {
                        return `<p class="font-bold text-lg mb-4 text-gray-800">${paragraph.substring(2, paragraph.length - 2)}</p>`;
                      }
                      
                      // Handle regular paragraphs
                      if (paragraph.trim() && !paragraph.startsWith('#')) {
                        return `<p class="mb-6 text-gray-700 leading-relaxed">${paragraph
                          .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-800">$1</strong>')
                          .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
                        }</p>`;
                      }
                      
                      return paragraph;
                    })
                    .join('')
                }}
              />
            </div>
          </div>
        </ScrollReveal>

        {/* Image Gallery */}
        {article.galleryImages && article.galleryImages.length > 0 && (
          <ScrollReveal delay={500}>
            <div className="mb-16">
              <h3 className="text-2xl font-bold mb-6 text-gray-800">Related Images</h3>
              <div className="grid md:grid-cols-3 gap-4">
                {article.galleryImages.map((image, index) => (
                  <div key={index} className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-3 overflow-hidden hover:shadow-lg transition-all duration-300">
                    <img
                      src={image}
                      alt={`${article.title} - Image ${index + 1}`}
                      className="w-full h-48 object-cover rounded-xl"
                    />
                  </div>
                ))}
              </div>
            </div>
          </ScrollReveal>
        )}

        {/* Article Navigation */}
        <ScrollReveal delay={600}>
          <div className="flex flex-col md:flex-row gap-4 mb-16">
            {previousArticle && (
              <Link 
                href={`/blog/${previousArticle.id}`}
                className="flex-1 group"
              >
                <div className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:border-green-200">
                  <div className="flex items-center gap-3 mb-3">
                    <ChevronLeft className="w-4 h-4 text-green-600 group-hover:-translate-x-1 transition-transform" />
                    <span className="text-sm text-gray-500">Previous Article</span>
                  </div>
                  <h3 className="font-semibold text-gray-800 group-hover:text-green-600 transition-colors line-clamp-2">
                    {previousArticle.title}
                  </h3>
                </div>
              </Link>
            )}

            {nextArticle && (
              <Link 
                href={`/blog/${nextArticle.id}`}
                className="flex-1 group"
              >
                <div className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:border-green-200">
                  <div className="flex items-center justify-end gap-3 mb-3">
                    <span className="text-sm text-gray-500">Next Article</span>
                    <ChevronRight className="w-4 h-4 text-green-600 group-hover:translate-x-1 transition-transform" />
                  </div>
                  <h3 className="font-semibold text-gray-800 group-hover:text-green-600 transition-colors line-clamp-2 text-right">
                    {nextArticle.title}
                  </h3>
                </div>
              </Link>
            )}
          </div>
        </ScrollReveal>

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <ScrollReveal delay={800}>
            <section className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-gray-800 flex items-center gap-3">
                <BookOpen className="w-8 h-8 text-green-600" />
                Related Articles
              </h2>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {relatedArticles.map((relatedArticle) => (
                  <Link 
                    key={relatedArticle.id}
                    href={`/blog/${relatedArticle.id}`}
                    className="group"
                  >
                    <article className="bg-white/70 backdrop-blur-sm border border-green-100 rounded-2xl overflow-hidden hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                      <div className="p-6">
                        <div className="text-3xl mb-4 text-center">{relatedArticle.image}</div>
                        
                        <div className="flex items-center gap-3 mb-3">
                          <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-lime-100 text-green-700 rounded-full text-xs font-medium">
                            {relatedArticle.category}
                          </span>
                          <div className="flex items-center gap-1 text-gray-400 text-xs">
                            <Clock className="w-3 h-3" />
                            {relatedArticle.readTime}
                          </div>
                        </div>
                        
                        <h3 className="text-lg font-bold text-gray-800 mb-3 group-hover:text-green-600 transition-colors line-clamp-2">
                          {relatedArticle.title}
                        </h3>
                        
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                          {relatedArticle.description}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500 mt-4">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(relatedArticle.date).toLocaleDateString()}
                          </div>
                          <span>by {relatedArticle.author}</span>
                        </div>
                      </div>
                    </article>
                  </Link>
                ))}
              </div>
            </section>
          </ScrollReveal>
        )}

        {/* Call to Action */}
        <ScrollReveal delay={1000}>
          <div className="bg-gradient-to-r from-green-500 to-lime-500 rounded-3xl p-8 text-white text-center">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Ready to Create Your Own Brat Covers?</h3>
            <p className="text-lg mb-6 max-w-2xl mx-auto">
              Put what you've learned into practice with our free Brat Generator tool. Create stunning covers in minutes!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/"
                className="bg-white text-green-600 px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                Try Brat Generator
              </Link>
              <Link 
                href="/blog"
                className="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-green-600 transition-all duration-200"
              >
                Read More Articles
              </Link>
            </div>
          </div>
        </ScrollReveal>
      </article>

    </div>
  );
}