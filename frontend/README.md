# Brat Generator Frontend

A modern React application for creating custom brat-style covers, built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 📁 Project Structure

```
src/
├── app/                     # Next.js App Router
│   ├── page.tsx            # Homepage
│   ├── auth/               # Authentication pages
│   ├── blog/               # Blog pages
│   ├── layout.tsx          # Root layout
│   └── globals.css         # Global styles
├── components/             # Reusable components
│   ├── ScrollReveal.tsx    # Scroll animation component
│   ├── AnimatedSection.tsx # Section animations
│   └── StaggeredReveal.tsx # Staggered animations
└── lib/                    # Utility libraries
```

## 🎨 Key Features

- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Smooth Animations**: Custom scroll reveals and micro-interactions
- **TypeScript**: Full type safety
- **Performance**: Optimized with Next.js 15 and Turbopack
- **SEO**: Built-in SEO optimization

## 🛠️ Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run Jest tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage

## 🎯 Customization

### Branding
1. Update app name in `layout.tsx` and `page.tsx`
2. Replace logo/icon in header components
3. Customize colors in `tailwind.config.ts`

### Content
1. Edit homepage content in `page.tsx`
2. Update blog posts in `blog/page.tsx`
3. Customize navigation in header components

### Styling
1. Modify global styles in `globals.css`
2. Update Tailwind configuration
3. Add custom animations and transitions

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env.local`:

```bash
cp .env.example .env.local
```

### Tailwind CSS
Customize colors, fonts, and spacing in `tailwind.config.ts`:

```typescript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: '#your-color',
        secondary: '#your-color',
      },
    },
  },
}
```

## 📱 Responsive Design

The template uses a mobile-first approach:

- **Mobile**: Base styles
- **Tablet**: `sm:` and `md:` breakpoints
- **Desktop**: `lg:` and `xl:` breakpoints

## ⚡ Performance

- **Next.js 15**: Latest performance optimizations
- **Turbopack**: Fast development builds
- **Image Optimization**: Automatic image optimization
- **Code Splitting**: Automatic code splitting
- **Tree Shaking**: Remove unused code

## 🧪 Testing

Run tests with Jest and React Testing Library:

```bash
# Run all tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage
```

## 🌍 Deployment

### Vercel (Recommended)
1. Connect repository to Vercel
2. Set environment variables
3. Deploy automatically

### Other Platforms
- **Netlify**: Configure build settings
- **AWS Amplify**: Add build configuration
- **Cloudflare Pages**: Set build commands

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com)
- [TypeScript](https://www.typescriptlang.org)
- [React](https://reactjs.org)